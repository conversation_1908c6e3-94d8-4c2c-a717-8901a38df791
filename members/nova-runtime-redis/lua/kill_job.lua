--[[
杀死/终止任务

这个脚本用于强制终止一个正在执行的任务，将其标记为死亡状态。
这通常用于处理无法恢复的错误或需要强制停止的任务。

参数说明：
KEYS[1]: 当前消费者的执行中任务集合 (set) - 存储该工作者正在处理的任务 inflight
KEYS[2]: 死亡任务集合 (sorted set) - 存储被终止的任务，按终止时间排序 dead
KEYS[3]: 任务数据哈希表 (hash) - 存储任务相关数据的基础键  data

ARGV[1]: 任务ID - 要终止的任务标识
ARGV[2]: 当前时间戳 - 任务被终止的时间
ARGV[3]: 终止原因或错误信息 - 说明任务被终止的原因

返回值：
1 - 任务成功被终止
0 - 任务不在执行中状态（可能已完成或不存在）
--]]

-- 从当前消费者的执行中任务集合中移除该任务
-- 确保任务确实在该消费者的执行列表中
local removed = redis.call("srem", KEYS[1], ARGV[1])

-- 如果任务确实从执行中集合移除成功
if removed == 1 then
    -- 将任务添加到死亡任务集合，使用终止时间作为分数
    -- 这样可以按时间顺序查询被终止的任务，便于故障分析
    redis.call("zadd", KEYS[2], ARGV[2], ARGV[1])

    -- 保存任务的终止信息（错误原因、堆栈跟踪等）
    -- 使用命名空间分离正常结果和错误信息
    -- 注意：使用 hset 替代已废弃的 hmset 命令
    local ns = "::result"
    redis.call("hset", KEYS[3] .. ns, ARGV[1], ARGV[3])

    return 1
end

-- 任务不在执行中状态，无法终止
return 0
