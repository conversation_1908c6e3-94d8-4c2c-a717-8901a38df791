// use futures::FutureExt;
use std::fmt;
use std::marker::PhantomData;
use std::task::Context;
use std::task::Poll;

use futures::FutureExt;
use futures::future::Map;
use tower::Service;

use crate::error::NovaRuntimeError;
use crate::layers::SharedData;
use crate::task::IntoResponse;
use crate::task::TaskRequest;

/// 异步函数构造tower服务
pub fn service_fn<T, Req, Ctx, FnArgs>(f: T) -> ServiceFn<T, Req, Ctx, FnArgs> {
    ServiceFn {
        f,
        req: PhantomData,
        fn_args: PhantomData,
    }
}

/// 异步函数构造tower服务
#[derive(Co<PERSON>, Clone)]
pub struct ServiceFn<T, Req, Ctx, FnArgs> {
    f: T,
    req: PhantomData<TaskRequest<Req, Ctx>>,
    fn_args: PhantomData<FnArgs>,
}

impl<T, Req, Ctx, FnArgs> fmt::Debug for ServiceFn<T, Req, Ctx, FnArgs> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("ServiceFn")
            .field("f", &format_args!("{}", std::any::type_name::<T>()))
            .finish()
    }
}

/// The Future returned from [`ServiceFn`] service.
pub type FnFuture<F, O, R, E> = Map<F, fn(O) -> Result<R, E>>;

impl<T, F, Req, E, R, Ctx> Service<TaskRequest<Req, Ctx>> for ServiceFn<T, Req, Ctx, ()>
where
    T: FnMut(Req) -> F,
    F: Future,
    F::Output: IntoResponse<Result = Result<R, E>>,
{
    type Response = R;
    type Error = E;
    type Future = FnFuture<F, F::Output, R, E>;

    fn poll_ready(&mut self, _: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        Poll::Ready(Ok(()))
    }

    fn call(&mut self, task: TaskRequest<Req, Ctx>) -> Self::Future {
        let fut = (self.f)(task.args);
        fut.map(F::Output::into_response)
    }
}

/// Handles extraction
pub trait FromRequest<Req>: Sized {
    /// Perform the extraction.
    fn from_request(req: &Req) -> Result<Self, NovaRuntimeError>;
}

impl<T: Clone + Send + Sync + 'static, Req, Ctx> FromRequest<TaskRequest<Req, Ctx>>
    for SharedData<T>
{
    fn from_request(req: &TaskRequest<Req, Ctx>) -> Result<Self, NovaRuntimeError> {
        req.parts.data.get_checked().cloned().map(SharedData::new)
    }
}
