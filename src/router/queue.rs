use std::sync::Arc;
use axum::{
    extract::{Path, State},
    response::IntoResponse,
    routing::{get, post},
    Json, Router,
};
use http::StatusCode;
use serde_json::json;
use tracing::{info, warn};

use crate::{
    queue::{Task, TaskRequest, TaskResponse, SyncTaskRequest, SyncTaskResponse, TaskType},
    AppState, ServerResponse,
};

pub fn get_queue_router() -> Router<Arc<AppState>> {
    Router::new()
        .route("/tasks", post(submit_task))
        .route("/tasks/sync", post(submit_sync_task))
        .route("/tasks", get(get_all_tasks))
        .route("/tasks/{task_id}", get(get_task))
        .route("/tasks/{task_id}/cancel", post(cancel_task))
        .route("/tasks/{task_id}/retry", post(retry_task))
        .route("/stats", get(get_queue_stats))
        .route("/performance", get(get_performance_metrics))
        .route("/health", get(get_health_check))
}

/// 提交新任务到队列
async fn submit_task(
    State(state): State<Arc<AppState>>,
    Json(request): Json<TaskRequest>,
) -> impl IntoResponse {
    info!("Received task submission request: {:?}", request.task_type);

    let mut task = Task::new(request.task_type, request.payload);
    
    if let Some(max_retries) = request.max_retries {
        task.max_retries = max_retries;
    }

    match state.queue_manager.submit_task(task).await {
        task_id => {
            let response = TaskResponse {
                task_id: task_id.clone(),
                status: crate::queue::TaskStatus::Pending,
                message: "Task submitted successfully".to_string(),
            };

            info!("Task {} submitted successfully", task_id);

            (
                StatusCode::CREATED,
                Json(ServerResponse::new("success", json!({
                    "task_id": response.task_id,
                    "status": response.status,
                    "message": response.message
                }))),
            )
        }
    }
}

/// 提交任务并等待执行完成后返回结果
async fn submit_sync_task(
    State(state): State<Arc<AppState>>,
    Json(request): Json<SyncTaskRequest>,
) -> impl IntoResponse {
    info!("Received sync task submission request: {:?}", request.task_type);

    // 检查是否可以接受新的同步请求
    if !state.queue_manager.can_accept_sync_request().await {
        let active_count = state.queue_manager.get_active_sync_count().await;
        warn!("Sync request limit reached (active: {}), falling back to async mode", active_count);

        // 降级到异步模式
        let async_request = TaskRequest {
            task_type: request.task_type,
            payload: request.payload,
            max_retries: request.max_retries,
        };

        let mut task = Task::new(async_request.task_type, async_request.payload);
        if let Some(max_retries) = async_request.max_retries {
            task.max_retries = max_retries;
        }
        let task_id = state.queue_manager.submit_task(task).await;

        return (
            StatusCode::ACCEPTED, // 202 表示已接受但降级到异步处理
            Json(ServerResponse::new("fallback", json!({
                "task_id": task_id,
                "status": "Pending",
                "message": "Request fallback to async mode due to high load",
                "note": "Use GET /api/v1/queue/tasks/{task_id} to check status"
            }))),
        );
    }

    let start_time = std::time::Instant::now();
    let mut task = Task::new(request.task_type, request.payload);

    if let Some(max_retries) = request.max_retries {
        task.max_retries = max_retries;
    }

    let timeout_seconds = request.timeout_seconds.unwrap_or(30); // 默认30秒超时，降低资源占用
    let task_id = task.id.clone();

    info!("Submitting sync task {} with timeout {}s", task_id, timeout_seconds);

    match state.queue_manager.submit_and_wait(task, timeout_seconds).await {
        Some(completed_task) => {
            let execution_time_ms = start_time.elapsed().as_millis() as u64;

            match completed_task.status {
                crate::queue::TaskStatus::Completed => {
                    let response = SyncTaskResponse {
                        task_id: completed_task.id,
                        status: completed_task.status,
                        result: completed_task.result,
                        error: None,
                        execution_time_ms,
                        message: "Task completed successfully".to_string(),
                    };

                    info!("Sync task {} completed in {}ms", task_id, execution_time_ms);

                    (
                        StatusCode::OK,
                        Json(ServerResponse::new("success", json!({
                            "task_id": response.task_id,
                            "status": response.status,
                            "result": response.result,
                            "execution_time_ms": response.execution_time_ms,
                            "message": response.message
                        }))),
                    )
                }
                crate::queue::TaskStatus::Failed => {
                    let response = SyncTaskResponse {
                        task_id: completed_task.id,
                        status: completed_task.status,
                        result: None,
                        error: completed_task.error,
                        execution_time_ms,
                        message: "Task failed".to_string(),
                    };

                    info!("Sync task {} failed in {}ms", task_id, execution_time_ms);

                    (
                        StatusCode::OK,
                        Json(ServerResponse::new("error", json!({
                            "task_id": response.task_id,
                            "status": response.status,
                            "error": response.error,
                            "execution_time_ms": response.execution_time_ms,
                            "message": response.message
                        }))),
                    )
                }
                _ => {
                    // 任务被取消或其他状态
                    let task_status = completed_task.status.clone();
                    let response = SyncTaskResponse {
                        task_id: completed_task.id,
                        status: completed_task.status,
                        result: None,
                        error: completed_task.error,
                        execution_time_ms,
                        message: format!("Task ended with status: {:?}", task_status),
                    };

                    (
                        StatusCode::OK,
                        Json(ServerResponse::new("info", json!({
                            "task_id": response.task_id,
                            "status": response.status,
                            "error": response.error,
                            "execution_time_ms": response.execution_time_ms,
                            "message": response.message
                        }))),
                    )
                }
            }
        }
        None => {
            let execution_time_ms = start_time.elapsed().as_millis() as u64;

            (
                StatusCode::REQUEST_TIMEOUT,
                Json(ServerResponse::error(json!({
                    "error": "Task execution timeout or task not found",
                    "task_id": task_id,
                    "timeout_seconds": timeout_seconds,
                    "execution_time_ms": execution_time_ms
                }))),
            )
        }
    }
}

/// 获取任务详情
async fn get_task(
    State(state): State<Arc<AppState>>,
    Path(task_id): Path<String>,
) -> impl IntoResponse {
    info!("Getting task details for: {}", task_id);

    match state.queue_manager.get_task(&task_id).await {
        Some(task) => (
            StatusCode::OK,
            Json(ServerResponse::new("success", json!({
                "task": task
            }))),
        ),
        None => (
            StatusCode::NOT_FOUND,
            Json(ServerResponse::error(json!({
                "error": "Task not found",
                "task_id": task_id
            }))),
        ),
    }
}

/// 获取所有任务
async fn get_all_tasks(State(state): State<Arc<AppState>>) -> impl IntoResponse {
    info!("Getting all tasks");

    let tasks = state.queue_manager.get_all_tasks().await;
    
    (
        StatusCode::OK,
        Json(ServerResponse::new("success", json!({
            "tasks": tasks,
            "count": tasks.len()
        }))),
    )
}

/// 取消任务
async fn cancel_task(
    State(state): State<Arc<AppState>>,
    Path(task_id): Path<String>,
) -> impl IntoResponse {
    info!("Cancelling task: {}", task_id);

    let success = state.queue_manager.cancel_task(&task_id).await;
    
    if success {
        (
            StatusCode::OK,
            Json(ServerResponse::new("success", json!({
                "message": "Task cancelled successfully",
                "task_id": task_id
            }))),
        )
    } else {
        (
            StatusCode::BAD_REQUEST,
            Json(ServerResponse::error(json!({
                "error": "Task cannot be cancelled or not found",
                "task_id": task_id
            }))),
        )
    }
}

/// 重试任务
async fn retry_task(
    State(state): State<Arc<AppState>>,
    Path(task_id): Path<String>,
) -> impl IntoResponse {
    info!("Retrying task: {}", task_id);

    let success = state.queue_manager.retry_task(&task_id).await;
    
    if success {
        (
            StatusCode::OK,
            Json(ServerResponse::new("success", json!({
                "message": "Task retry initiated successfully",
                "task_id": task_id
            }))),
        )
    } else {
        (
            StatusCode::BAD_REQUEST,
            Json(ServerResponse::error(json!({
                "error": "Task cannot be retried or not found",
                "task_id": task_id
            }))),
        )
    }
}

/// 获取队列统计信息
async fn get_queue_stats(State(state): State<Arc<AppState>>) -> impl IntoResponse {
    info!("Getting queue statistics");

    let stats = state.queue_manager.get_stats().await;

    (
        StatusCode::OK,
        Json(ServerResponse::new("success", json!({
            "stats": stats
        }))),
    )
}

/// 获取性能指标
async fn get_performance_metrics(State(state): State<Arc<AppState>>) -> impl IntoResponse {
    info!("Getting performance metrics");

    let metrics = state.queue_manager.get_performance_metrics().await;
    let stats = state.queue_manager.get_stats().await;

    (
        StatusCode::OK,
        Json(ServerResponse::new("success", json!({
            "performance": {
                "sync_request_count": metrics.sync_request_count,
                "total_execution_time_ms": metrics.total_execution_time_ms,
                "avg_execution_time_ms": stats.avg_execution_time_ms,
                "active_sync_requests": stats.active_sync_requests,
                "sync_capacity_available": state.queue_manager.can_accept_sync_request().await,
                "notification_count": metrics.notification_count,
                "polling_fallback_count": metrics.polling_fallback_count,
                "notification_efficiency": if metrics.sync_request_count > 0 {
                    (metrics.notification_count as f64 / metrics.sync_request_count as f64) * 100.0
                } else { 0.0 }
            }
        }))),
    )
}

/// 健康检查端点
async fn get_health_check(State(state): State<Arc<AppState>>) -> impl IntoResponse {
    let stats = state.queue_manager.get_stats().await;
    let active_sync = stats.active_sync_requests;
    let can_accept_sync = state.queue_manager.can_accept_sync_request().await;

    let health_status = if active_sync > 80 {
        "degraded" // 同步请求过多，性能下降
    } else if !can_accept_sync {
        "overloaded" // 无法接受新的同步请求
    } else {
        "healthy"
    };

    let status_code = match health_status {
        "healthy" => StatusCode::OK,
        "degraded" => StatusCode::OK,
        "overloaded" => StatusCode::SERVICE_UNAVAILABLE,
        _ => StatusCode::OK,
    };

    (
        status_code,
        Json(ServerResponse::new("info", json!({
            "status": health_status,
            "active_sync_requests": active_sync,
            "can_accept_sync": can_accept_sync,
            "total_tasks": stats.total_tasks,
            "avg_execution_time_ms": stats.avg_execution_time_ms,
            "timestamp": chrono::Utc::now().to_rfc3339()
        }))),
    )
}
