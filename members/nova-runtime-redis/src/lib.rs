mod expose;
mod redis_task_source;

use fred::prelude::Client;
use fred::types::scripts::Library;
use fred::error::RedisError;
pub use redis::{aio::ConnectionManager};
use redis::RedisError;
use tracing::error;
pub use redis_task_source::Config;
pub use redis_task_source::RedisContext;
pub use redis_task_source::RedisPollError;
pub use redis_task_source::RedisQueueInfo;
pub use redis_task_source::RedisStorage;

pub use redis_task_source::RedisScript;

/// 创建redis脚本
/// 创建 Redis 脚本库
///
/// 从 Lua 脚本文件创建 Redis 脚本库，用于任务队列的各种操作。
/// 所有脚本都会预编译并缓存在 Redis 服务器中，提高执行效率。
///
/// # 参数
/// - `client`: Redis 客户端引用
///
/// # 返回值
/// 返回包含所有预编译脚本的 `RedisScript` 结构体
///
/// # 错误
/// 如果任何脚本编译失败，将返回 Redis 错误
pub async fn create_redis_script(client: &Client) -> Result<RedisScript, RedisError> {
    // 并行加载所有 Lua 脚本以提高性能
   let res =  tokio::try_join!(
        Library::from_code(client, include_str!("../lua/done_job.lua")),
        Library::from_code(client, include_str!("../lua/push_job.lua")),
        Library::from_code(client, include_str!("../lua/retry_job.lua")),
        Library::from_code(client, include_str!("../lua/enqueue_scheduled_jobs.lua")),
        Library::from_code(client, include_str!("../lua/get_jobs.lua")),
        Library::from_code(client, include_str!("../lua/register_consumer.lua")),
        Library::from_code(client, include_str!("../lua/kill_job.lua")),
        Library::from_code(client, include_str!("../lua/reenqueue_active_jobs.lua")),
        Library::from_code(client, include_str!("../lua/reenqueue_orphaned_jobs.lua")),
        Library::from_code(client, include_str!("../lua/schedule_job.lua")),
        Library::from_code(client, include_str!("../lua/vacuum.lua")),
        Library::from_code(client, include_str!("../lua/stats.lua")),
    ).map_err(|e| {
        error!("Failed to create Redis script: {}", e);
        e
    });

    Ok(RedisScript {
        done_job,
        push_job,
        retry_job,
        enqueue_scheduled,
        get_jobs,
        register_consumer,
        kill_job,
        re_enqueue_active: reenqueue_active,
        re_enqueue_orphaned: reenqueue_orphaned,
        schedule_job,
        vacuum,
        stats,
    })
}
