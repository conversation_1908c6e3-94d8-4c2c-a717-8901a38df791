use std::marker::PhantomData;

use crate::layers::SharedData;
use crate::service_fn::{service_fn, ServiceFn};
use crate::task::{TaskRequest, TaskSource};
use crate::worker::{Ready, Worker, WorkerConfig};
use tower::{
    layer::util::{Identity, Stack},
    Layer, Service, ServiceBuilder,
};

/// 任务执行器构建器
pub struct WorkerBuilder<Req, Ctx, Source, Middleware, Serv> {
    pub(crate) name: String,
    pub(crate) request: PhantomData<TaskRequest<Req, Ctx>>,
    pub(crate) layer: ServiceBuilder<Middleware>,
    pub(crate) source: Source,
    pub(crate) config: WorkerConfig,
    service: PhantomData<Serv>,
}

impl<Req, Ctx, Source, Middleware, Serv> std::fmt::Debug
for WorkerBuilder<Req, Ctx, Source, Middleware, Serv>
{
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("WorkerBuilder")
            .field("name", &self.name)
            .field("job", &std::any::type_name::<Req>())
            .field("layer", &std::any::type_name::<Middleware>())
            .field("mq", &std::any::type_name::<Source>())
            .finish()
    }
}

impl<Serv> WorkerBuilder<(), (), (), Identity, Serv> {

    /// 初始化任务执行器构建器
    pub fn new<T: AsRef<str>>(name: T,config: WorkerConfig) -> WorkerBuilder<(), (), (), Identity, Serv> {
        let job: PhantomData<TaskRequest<(), ()>> = PhantomData;
        WorkerBuilder {
            request: job,
            config,
            layer: ServiceBuilder::new(),
            source: (),
            name: name.as_ref().to_string(),
            service: PhantomData,
        }
    }
}

impl<M, Serv> WorkerBuilder<(), (), (), M, Serv> {


    /// Set the mq to a backend that implements [Backend]
    pub fn mq<NB: TaskSource<TaskRequest<NJ, Ctx>>, NJ, Res: Send, Ctx>(
        self,
        mq: NB,
    ) -> WorkerBuilder<NJ, Ctx, NB, M, Serv>
    where
        Serv: Service<TaskRequest<NJ, Ctx>, Response = Res>,
    {
        WorkerBuilder {
            request: PhantomData,
            layer: self.layer,
            source: mq,
            name: self.name,
            config: self.config,
            service: self.service,
        }
    }
}

impl<Req, M, Serv, Ctx> WorkerBuilder<Req, Ctx, (), M, Serv> {
    /// Allows of decorating the service that consumes jobs.
    /// Allows adding multiple [`tower`] middleware
    pub fn chain<NewLayer>(
        self,
        f: impl FnOnce(ServiceBuilder<M>) -> ServiceBuilder<NewLayer>,
    ) -> WorkerBuilder<Req, Ctx, (), NewLayer, Serv> {
        let middleware = f(self.layer);

        WorkerBuilder {
            request: self.request,
            layer: middleware,
            name: self.name,
            source: self.source,
            config: self.config,
            service: self.service,
        }
    }
    /// Allows adding a single layer [tower] middleware
    pub fn layer<U>(self, layer: U) -> WorkerBuilder<Req, Ctx, (), Stack<U, M>, Serv>
    where
        M: Layer<U>,
    {
        WorkerBuilder {
            request: self.request,
            source: self.source,
            layer: self.layer.layer(layer),
            config: self.config,
            name: self.name,
            service: self.service,
        }
    }

    /// Adds data to the context
    /// This will be shared by all requests
    pub fn data<D>(self, data: D) -> WorkerBuilder<Req, Ctx, (), Stack<SharedData<D>, M>, Serv>
    where
        M: Layer<SharedData<D>>,
    {
        WorkerBuilder {
            request: self.request,
            source: self.source,
            layer: self.layer.layer(SharedData::new(data)),
            name: self.name,
            config: self.config,
            service: self.service,
        }
    }
}

impl<Req, P, M, S, Ctx> WorkerFactory<Req, Ctx, S> for WorkerBuilder<Req, Ctx, P, M, S>
where
    S: Service<TaskRequest<Req, Ctx>>,
    M: Layer<S>,
    P: TaskSource<TaskRequest<Req, Ctx>>,
{
    type Source = P;

    type Service = M::Service;

    ///构造任务执行器
    fn build(self, service: S) -> Worker<Ready<M::Service, P>> {
        let worker_id = self.name;
        let poller = self.source;
        let middleware = self.layer;
        let service = middleware.service(service);

        Worker::new(worker_id, self.config, Ready::new(service, poller))
    }
}
/// 传递处理服务构造执行器
pub trait WorkerFactory<Req, Ctx, S> {
    /// mq资源
    type Source;

    /// 处理任务服务
    type Service;

    ///构造执行者
    fn build(self, service: S) -> Worker<Ready<Self::Service, Self::Source>>;
}

/// 传递异步函数构造任务执行器
pub trait WorkerFactoryFn<Req, Ctx, F, FnArgs> {
    /// mq资源
    type Source;

    /// 处理任务服务
    type Service;


    /// 异步函数构造任务执行器
    fn build_fn(self, f: F) -> Worker<Ready<Self::Service, Self::Source>>;
}

impl<Req, W, F, Ctx, FnArgs> WorkerFactoryFn<Req, Ctx, F, FnArgs> for W
where
    W: WorkerFactory<Req, Ctx, ServiceFn<F, Req, Ctx, FnArgs>>,
{
    type Source = W::Source;

    type Service = W::Service;

    ///构造任务执行器
    fn build_fn(self, f: F) -> Worker<Ready<Self::Service, Self::Source>> {
        self.build(service_fn(f))
    }
}
