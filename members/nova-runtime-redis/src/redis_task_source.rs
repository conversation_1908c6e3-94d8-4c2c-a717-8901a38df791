use std::any::type_name;
use std::fmt::Debug;
use std::fmt::{self};
use std::io;
use std::marker::PhantomData;
use std::num::TryFromIntError;
use std::task::Context;
use std::time::{Duration, SystemTime};

use chrono::DateTime;
use chrono::Utc;
use fred::bytes_utils::Str;
use fred::error::Error as RedisError;
use fred::error::ErrorKind;
use fred::prelude::HashesInterface;
use fred::prelude::ListInterface;
use fred::prelude::LuaInterface;
use fred::prelude::SetsInterface;
use fred::prelude::SortedSetsInterface;
use fred::prelude::{Client, Value};
use fred::types::scripts::Library;
use futures::channel::mpsc::SendError;
use futures::channel::mpsc::Sender;
use futures::channel::mpsc::{self};
use futures::select;
use futures::FutureExt;
use futures::SinkExt;
use futures::StreamExt;
use futures::TryFutureExt;
use log::info;
use log::warn;
use nova_runtime::codec::Codec;
use nova_runtime::codec::JsonCodec;
use nova_runtime::error::NovaRuntimeError;
use nova_runtime::layers::Ack;
use nova_runtime::layers::AckLayer;
use nova_runtime::service_fn::FromRequest;
use nova_runtime::task::Parts;
use nova_runtime::task::TaskId;
use nova_runtime::task::TaskRequest;
use nova_runtime::task::TaskResult;
use nova_runtime::task::TaskSource;
use nova_runtime::task::TaskSourceStream;
use nova_runtime::worker::Event;
use nova_runtime::worker::Poller;
use nova_runtime::worker::PollerState;
use nova_runtime::worker::Worker;
use nova_runtime::BoxDynError;
use nova_runtime::Storage;
use nova_runtime::TaskRequestStream;
// use redis::Value;
use redis::aio::ConnectionLike;
use redis::aio::ConnectionManager;
use redis::IntoConnectionInfo;
use redis::Script;
use serde::de::DeserializeOwned;
use serde::Deserialize;
use serde::Serialize;
use tracing::{debug, error};
use crate::config::Config;
use crate::RedisPollError;

/// redis脚本
#[derive(Clone, Debug)]
pub struct RedisScript {
    pub done_job: String,
    pub get_jobs: String,
    pub kill_job: String,
    pub push_job: String,
    pub vacuum: String,
    pub stats: String,
}

/// 任务上下文
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RedisContext {
    max_attempts: usize,
    lock_by: Option<String>,
    run_at: Option<SystemTime>,
}

impl RedisContext {
    pub fn new(lock_by: Option<String>) -> Self {
        Self {
            lock_by,
            run_at: Some(SystemTime::now()),
            ..Self::default()
        }
    }
}

impl Default for RedisContext {
    fn default() -> Self {
        Self {
            max_attempts: 1,
            lock_by: None,
            run_at: None,
        }
    }

}

/// 从请求中获取上下文
impl<Req> FromRequest<TaskRequest<Req, RedisContext>> for RedisContext {
    fn from_request(req: &TaskRequest<Req, RedisContext>) -> Result<Self, NovaRuntimeError> {
        Ok(req.parts.context.clone())
    }
}



/// Represents a [Storage] that uses Redis for storage.
pub struct RedisStorage<T, C = JsonCodec<Vec<u8>>> {
    client: Client,
    job_type: PhantomData<T>,
    pub(super) scripts: RedisScript,
    poller_state: PollerState,
    config: Config,
    codec: PhantomData<C>,
}

impl<T, C> Debug for RedisStorage<T, C> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("RedisStorage")
            .field("job_type", &std::any::type_name::<T>())
            .field("scripts", &self.scripts)
            .field("config", &self.config)
            .finish()
    }
}

impl<T, C> Clone for RedisStorage<T, C> {
    fn clone(&self) -> Self {
        Self {
            client: self.client.clone(),
            job_type: PhantomData,
            scripts: self.scripts.clone(),
            poller_state: self.poller_state.clone(),
            config: self.config.clone(),
            codec: self.codec,
        }
    }
}

impl<T: Serialize + DeserializeOwned> RedisStorage<T, JsonCodec<Vec<u8>>> {
    /// 创建一个新的 RedisStorage 实例，并验证 Redis 连接
    pub async fn new(
        redis_client: Client,
        scripts: RedisScript,
    ) -> RedisStorage<T, JsonCodec<Vec<u8>>> {
        Self::new_with_codec::<JsonCodec<Vec<u8>>>(
            redis_client,
            Config::default().set_namespace(type_name::<T>()),
            scripts,
        )
    }

    pub fn new_with_codec<K>(
        client: Client,
        config: Config,
        scripts: RedisScript,
    ) -> RedisStorage<T, K>
    where
        K: Codec + Sync + Send + 'static,
    {
        RedisStorage {
            client,
            job_type: PhantomData,
            poller_state: PollerState::new(),
            config,
            codec: PhantomData::<K>,
            scripts: scripts.clone(),
        }
    }

    /// 获取数据源
    pub fn get_connection(&self) -> &Client {
        &self.client
    }

    /// 获取配置
    pub fn get_config(&self) -> &Config {
        &self.config
    }
}

impl<T, C> TaskSource<TaskRequest<T, RedisContext>> for RedisStorage<T, C>
where
    T: Serialize + DeserializeOwned + Sync + Send + Unpin + 'static,
    C: Codec<Compact = Vec<u8>> + Send + 'static,
{
    type Stream = TaskSourceStream<TaskRequestStream<TaskRequest<T, RedisContext>>>;

    type Layer = AckLayer<Sender<(RedisContext, TaskResult<Vec<u8>>)>, T, RedisContext, C>;

    type Codec = C;

    /// 此方法会轮询 Redis 队列，拉取任务并返回任务流
    fn poll(
        mut self,
        worker: &Worker<nova_runtime::worker::Context>,
    ) -> Poller<Self::Stream, Self::Layer> {
        let worker = worker.clone();
       let worker_config =  worker.get_config();
        let buffer_size = worker_config.limit as usize;
        let (mut tx, rx) = mpsc::channel(buffer_size);
        let (ack, ack_rx) =
            mpsc::channel::<(RedisContext, TaskResult<Vec<u8>>)>(buffer_size);

        let layer = AckLayer::new(ack);
        let poller_state = self.poller_state.clone();
        let config = self.config.clone();
        let stream: TaskRequestStream<TaskRequest<T, RedisContext>> = Box::pin(rx);

        // 心跳任务
        let heartbeat = async move {
            // 心跳周期
            let mut keep_alive_stm = nova_runtime::interval::interval(config.keep_alive).fuse();

            // 拉取任务周期
            let mut poll_next_stm = nova_runtime::interval::interval(config.poll_interval).fuse();

            let mut ack_stream = ack_rx.fuse();

            loop {
                select! {
                    _ = poll_next_stm.next() => {
                        if worker.is_ready() {
                            let res = self.fetch_next(worker.name()).await;
                            match res {
                                Err(e) => {
                                    worker.emit(Event::Error(Box::new(RedisPollError::PollNextError(e))));
                                }
                                Ok(res) => {
                                    for job in res {
                                        if let Err(e) = tx.send(Ok(Some(job))).await {
                                            worker.emit(Event::Error(Box::new(RedisPollError::EnqueueError(e))));
                                        }
                                    }
                                }
                            }
                        } else {
                            continue;
                        }

                    }
                    id_to_ack = ack_stream.next() => {
                        if let Some((ctx, res)) = id_to_ack {
                            //接收到消息确认
                            if let Err(e) = self.ack(&ctx, &res).await {
                                worker.emit(Event::Error(Box::new(RedisPollError::AckError(e))));
                            }
                        }
                    }
                }
            }
        };

        //构造可ack的任务流
        Poller::new_with_layer(
            TaskSourceStream::new(stream, poller_state),
            heartbeat.boxed(),
            layer,
        )
    }
}

/// 此方法会处理任务的确认，更新任务状态并返回确认结果
impl<T, C, Res> Ack<T, Res, C> for RedisStorage<T, C>
where
    T: Sync + Send + Serialize + DeserializeOwned + Unpin + 'static,
    C: Codec<Compact = Vec<u8>> + Send + 'static,
    Res: Serialize + Sync + Send + 'static,
{
    type Context = RedisContext;
    type AckError = RedisError;

    /// 任务确认
    async fn ack(&mut self, ctx: &Self::Context, res: &TaskResult<Res>) -> Result<(), RedisError> {
        tracing::info!("ack task: {:?}", res.task_id);
        // let mut task = self
        //     .fetch_by_id(&res.task_id)
        //     .await?
        //     .expect("must be a valid task");

        // 跟新任务状态
        // self.update(task).await?;


        let now: i64 = Utc::now().timestamp();
        let task_id = res.task_id.to_string();
        match &res.inner {
            /// 任务成功
            Ok(success_res) => {
                let done_job = self.scripts.done_job.clone();
                let done_jobs_set = &self.config.done_jobs_set();
                let success_res = C::encode(success_res).map_err(Into::into).unwrap();
                let success_res = String::from_utf8(success_res)?;
                //任务完成，数据处理
                self.client
                    .evalsha(
                        &done_job,
                        &[
                            inflight_set.as_bytes(),
                            done_jobs_set.as_bytes(),
                            self.config.job_data_hash().as_bytes(),
                        ],
                        &[task_id, now.to_string(), success_res],
                    )
                    .await
            }
            /// 任务失败
            Err(e) => match e {
                NovaRuntimeError::Abort(e) => {
                    let worker_id = ctx.lock_by.as_ref().unwrap();
                    self.kill(worker_id, &res.task_id, &e).await
                }
                _ => {
                    if ctx.max_attempts > 1 {
                        let worker_id = ctx.lock_by.as_ref().unwrap();
                        self.retry(worker_id, &res.task_id).await.map(|_| ())
                    } else {
                        let worker_id = ctx.lock_by.as_ref().unwrap();

                        self.kill(
                            worker_id,
                            &res.task_id,
                            &(Box::new(io::Error::new(
                                io::ErrorKind::Interrupted,
                                format!("Max retries of {} exceeded", ctx.max_attempts),
                            )) as BoxDynError),
                        )
                        .await
                    }
                }
            },
        }
    }
}

impl<T, C> RedisStorage<T, C>
where
    T: DeserializeOwned + Send + Unpin + Send + Sync + 'static,
    C: Codec<Compact = Vec<u8>>,
{
    /// 从 Redis 队列中获取任务
    async fn fetch_next(
        &mut self,
        worker_id: &str,
    ) -> Result<Vec<TaskRequest<T, RedisContext>>, RedisError> {
        let fetch_jobs = self.scripts.get_jobs.clone();
        let active_jobs_list = self.config.active_jobs_list(worker_id);
        let job_data_hash = self.config.job_data_hash(worker_id);

        let result:Result<Vec<Value>, RedisError> = self
            .client
            .evalsha(
                &fetch_jobs,
                vec![
                    active_jobs_list,
                    job_data_hash,
                ],
                vec![self.config.buffer_size.to_string()],
            )
            .await;

        match result {
            Ok(jobs) => {
                let mut processed = vec![];
                for job in jobs {
                    let bytes = deserialize_job(&job)?;
                    let mut request: TaskRequest<T, RedisContext> =
                        C::decode(Vec::from(bytes.as_bytes())).map_err(|e| build_error(&e.into().to_string()))?;
                    processed.push(request)
                }
                Ok(processed)
            }
            Err(e) => {
                warn!("获取任务时发生错误: {}", e);
                Err(e)
            }
        }
    }
}

/// 构建 Redis 错误
fn build_error(message: &str) -> RedisError {
    RedisError::from(io::Error::new(io::ErrorKind::InvalidData, message))
}

/// 从 Redis 中反序列化任务请求
fn deserialize_job(job: &Value) -> Result<&Str, RedisError> {
    match job {
        Value::String(bytes) => Ok(bytes),
        Value::Array(val)=> val
            .first()
            .and_then(|val| {
                if let Value::String(bytes) = val {
                    Some(bytes)
                } else {
                    None
                }
            })
            .ok_or(build_error("Value::Bulk: Invalid data returned by storage")),
        _ => Err(build_error("unknown result type for next message")),
    }
}

impl<T, C> RedisStorage<T, C> {
    /// 保持活动状态
    /// 工作者在执行任务时，会定期调用此方法来更新其最后活跃时间
    async fn keep_alive(&mut self, worker_id: &str) -> Result<(), RedisError> {

        Ok(())
    }
}

impl<T, C> Storage for RedisStorage<T, C>
where
    T: Serialize + DeserializeOwned + Send + 'static + Unpin + Sync,
    C: Codec<Compact = Vec<u8>> + Send + 'static,
{
    type Job = T;
    type Error = RedisError;
    type Context = RedisContext;

    type Compact = Vec<u8>;

    /// 此方法会将任务请求推送到 Redis 队列中
    async fn push_request(
        &mut self,
        req: TaskRequest<T, RedisContext>,
    ) -> Result<Parts<Self::Context>, RedisError> {
        let push_job = self.scripts.push_job.clone();
        let ctx = req.parts.context.clone();
        let worker_id = ctx.lock_by.unwrap_or("default".to_string());
        let job_data_hash = self.config.job_data_hash(&worker_id);
        let active_jobs_list = self.config.active_jobs_list(&worker_id);
        let rev_jobs_set = self.config.rev_jobs_set(&worker_id);

        //序列化任务
        let job =
            C::encode(&req).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
        //添加任务
        let res: usize = self
            .client
            .evalsha(
                &push_job,
                vec![job_data_hash, active_jobs_list, rev_jobs_set, self.config.job_worker_list()],
                vec![req.parts.task_id.to_string(), String::from_utf8(job)?,worker_id],
            )
            .await?;
        if res == 0 {
            return Err(RedisError::new(
                ErrorKind::IO,
                "Failed to push job".to_string(),
            ));
        }
        Ok(req.parts)
    }

    async fn push_raw_request(
        &mut self,
        req: TaskRequest<Self::Compact, Self::Context>,
    ) -> Result<Parts<Self::Context>, Self::Error> {
       //  let push_job = self.scripts.push_job.clone();
       //  let job_data_hash = self.config.job_data_hash();
       //  let active_jobs_list = self.config.active_jobs_list();
       //  let signal_list = self.config.signal_list();
       //
       //  let job =
       //      C::encode(&req).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
       // let _: usize = self.client
       //      .evalsha(
       //          &push_job,
       //          &[job_data_hash, active_jobs_list, signal_list],
       //          vec![req.parts.task_id.to_string(), String::from_utf8(job)?],
       //      )
       //      .await?;
       //  Ok(req.parts)
        todo!()
    }

    fn schedule_request(&mut self, request: TaskRequest<Self::Job, Self::Context>, on: i64) -> impl Future<Output=Result<Parts<Self::Context>, Self::Error>> + Send {
        todo!()
    }


    /// 根据任务ID获取任务
    async fn fetch_by_id(
        &mut self,
        job_id: &TaskId,
    ) -> Result<Option<TaskRequest<Self::Job, RedisContext>>, RedisError> {

        let data: Option<Vec<u8>> = self
            .client
            .hget(self.config.job_data_hash("default"), job_id.to_string())
            .await?;
        // 反序列化任务
        let inner: TaskRequest<T, RedisContext> = C::decode(data.expect("must be a valid task"))
            .map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
        Ok(Some(inner))
    }

    /// 更新任务
    async fn update(&mut self, job: TaskRequest<T, RedisContext>) -> Result<(), RedisError> {
        let task_id = job.parts.task_id.to_string();
        let ctx = job.parts.context.clone();
        let worker_id = ctx.lock_by.unwrap_or("default".to_string());
        let bytes =
            C::encode(&job).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
       let _:i64 = self.client
            .hset(self.config.job_data_hash(&worker_id), (task_id, bytes))
            .await?;
        Ok(())
    }

    fn reschedule(&mut self, job: TaskRequest<Self::Job, Self::Context>, wait: Duration) -> impl Future<Output=Result<(), Self::Error>> + Send {
        todo!()
    }



    async fn vacuum(&mut self) -> Result<usize, RedisError> {
        let vacuum_script = self.scripts.vacuum.clone();
        // let res: usize = self
        //     .client
        //     .evalsha(
        //         &vacuum_script,
        //         vec![self.config.done_jobs_set(), self.config.job_data_hash()],
        //         vec![],
        //     )
        //     .await?;
        Ok(1)
    }
}

impl<T, C> RedisStorage<T, C>
where
    C: Codec<Compact = Vec<u8>> + Send + 'static,
{
    /// Attempt to retry a job
    pub async fn retry(&mut self, worker_id: &str, task_id: &TaskId) -> Result<i32, RedisError>
    where
        T: Send + DeserializeOwned + Serialize + Unpin + Sync + 'static,
    {
        todo!()
    }

    /// Attempt to kill a job
    pub async fn kill(
        &mut self,
        worker_id: &str,
        task_id: &TaskId,
        error: &BoxDynError,
    ) -> Result<(), RedisError> {
        todo!()
        // let kill_job = self.scripts.kill_job.clone();
        // let current_worker_id = format!("{}:{}", self.config.inflight_jobs_set(), worker_id);
        // let job_data_hash = self.config.job_data_hash();
        // let dead_jobs_set = self.config.dead_jobs_set();
        // let now: i64 = Utc::now().timestamp();
        // self.client
        //     .evalsha(
        //         &kill_job,
        //         vec![current_worker_id, dead_jobs_set, job_data_hash],
        //         vec![task_id.to_string(), now.to_string(), error.to_string()],
        //     )
        //     .await
    }


}
