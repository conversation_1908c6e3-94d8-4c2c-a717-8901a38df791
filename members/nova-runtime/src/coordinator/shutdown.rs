use std::pin::Pin;
use std::sync::Arc;
use std::sync::Mutex;
use std::sync::atomic::AtomicBool;
use std::sync::atomic::Ordering;
use std::task::Context;
use std::task::Poll;
use std::task::Waker;

/// 关闭上下文
/// 关闭上下文用于在任务协作器中管理关闭状态和关闭句柄
#[derive(Debug)]
pub(crate) struct ShutdownCtx {
    /// 关闭状态
    state: AtomicBool,
    /// 关闭句柄
    waker: Mutex<Option<Waker>>,
}
impl ShutdownCtx {
    fn new() -> ShutdownCtx {
        Self {
            state: AtomicBool::default(),
            waker: Mutex::default(),
        }
    }
    fn shutdown(&self) {
        self.state.store(true, Ordering::Relaxed);
        self.wake();
    }

    fn is_shutting_down(&self) -> bool {
        self.state.load(Ordering::Relaxed)
    }

    pub(crate) fn wake(&self) {
        if let Some(waker) = self.waker.lock().unwrap().take() {
            waker.wake();
        }
    }
}

/// 关闭句柄
#[derive(<PERSON><PERSON>, Debug)]
pub struct Shutdown {
    inner: Arc<ShutdownCtx>,
}

/// 关闭句柄实现Future trait
impl Future for Shutdown {
    type Output = ();

    fn poll(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<()> {
        let ctx = &self.inner;
        if ctx.state.load(Ordering::Relaxed) {
            Poll::Ready(())
        } else {
            *ctx.waker.lock().unwrap() = Some(cx.waker().clone());
            Poll::Pending
        }
    }
}

impl Shutdown {
    pub fn new() -> Shutdown {
        Shutdown {
            inner: Arc::new(ShutdownCtx::new()),
        }
    }

    /// 是否正在关闭
    pub fn is_shutting_down(&self) -> bool {
        self.inner.is_shutting_down()
    }

    /// 开始关闭
    pub fn start_shutdown(&self) {
        self.inner.shutdown()
    }

    /// 等待future完成后关闭
    pub fn shutdown_after<F: Future>(&self, f: F) -> impl Future<Output = F::Output>+ use<F> {
        let handle = self.clone();
        async move {
            let result = f.await;
            handle.start_shutdown();
            result
        }
    }
}

impl Default for Shutdown {
    fn default() -> Self {
        Self::new()
    }
}
