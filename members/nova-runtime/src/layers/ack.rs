use std::fmt::Debug;
use std::marker::PhantomData;
use std::sync::Arc;
use futures::channel::mpsc::{SendError, Sender};
use futures::future::BoxFuture;
use futures::{FutureExt, SinkExt};
use serde::Serialize;
use tower::{Layer, Service};
use crate::BoxDynError;
use crate::codec::Codec;
use crate::error::NovaRuntimeError;
use crate::task::{TaskRequest, TaskResult};

/// 任务确认层.
/// 任务确认层用于确认任务处理成功.
/// 当任务处理成功时,会调用`ack`方法.
/// 该方法用于确认任务处理成功,并返回一个错误.
pub trait Ack<Task, Res, Codec> {

    /// 确认任务处理成功所需的上下文数据
    type Context;

    /// 确认任务处理成功时可能返回的错误
    type AckError: std::error::Error;


    /// 该方法用于确认任务处理成功,并返回一个错误.
    fn ack(
        &mut self,
        ctx: &Self::Context,
        response: &TaskResult<Res>,
    ) -> impl Future<Output = Result<(), Self::AckError>> + Send;
}

///为`Sender<(Ctx, TaskResult<Cdc::Compact>)>`实现`Ack` trait.
impl<T, Res: Clone + Send + Sync + Serialize, Ctx: Clone + Send + Sync, Cdc: Codec> Ack<T, Res, Cdc>
for Sender<(Ctx, TaskResult<Cdc::Compact>)>
where
    Cdc::Error: Debug,
    Cdc::Compact: Send,
{
    type Context = Ctx;
    type AckError = SendError;
    async fn ack(
        &mut self,
        ctx: &Self::Context,
        result: &TaskResult<Res>,
    ) -> Result<(), Self::AckError> {
        let ctx = ctx.clone();
            let res = result.map(|res| Cdc::encode(res).unwrap());
        self.send((ctx, res)).await?;
        Ok(())
    }
}


#[derive(Debug)]
pub struct AckLayer<A, Req, Ctx, Cdc> {
    ack: A,
    job_type: PhantomData<TaskRequest<Req, Ctx>>,
    codec: PhantomData<Cdc>,
}

impl<A, Req, Ctx, Cdc> AckLayer<A, Req, Ctx, Cdc> {
    pub fn new(ack: A) -> Self {
        Self {
            ack,
            job_type: PhantomData,
            codec: PhantomData,
        }
    }
}

impl<A, Req, Ctx, S, Cdc> Layer<S> for AckLayer<A, Req, Ctx, Cdc>
where
    S: Service<TaskRequest<Req, Ctx>> + Send + 'static,
    S::Error: std::error::Error + Send + Sync + 'static,
    S::Future: Send + 'static,
    A: Ack<Req, S::Response, Cdc> + Clone + Send + Sync + 'static,
{
    type Service = AckService<S, A, Req, Ctx, Cdc>;

    fn layer(&self, service: S) -> Self::Service {
        AckService {
            service,
            ack: self.ack.clone(),
            job_type: PhantomData,
            codec: PhantomData,
        }
    }
}

/// 服务层
#[derive(Debug)]
pub struct AckService<SV, A, Req, Ctx, Cdc> {
    service: SV,
    ack: A,
    job_type: PhantomData<TaskRequest<Req, Ctx>>,
    codec: PhantomData<Cdc>,
}

impl<Sv: Clone, A: Clone, Req, Ctx, Cdc> Clone for AckService<Sv, A, Req, Ctx, Cdc> {
    fn clone(&self) -> Self {
        Self {
            ack: self.ack.clone(),
            job_type: PhantomData,
            service: self.service.clone(),
            codec: PhantomData,
        }
    }
}

impl<SV, A, Req, Ctx, Cdc> Service<TaskRequest<Req, Ctx>> for AckService<SV, A, Req, Ctx, Cdc>
where
    SV: Service<TaskRequest<Req, Ctx>> + Send + 'static,
    SV::Error: Into<BoxDynError> + Send + 'static,
    SV::Future: Send + 'static,
    A: Ack<Req, SV::Response, Cdc, Context = Ctx> + Send + 'static + Clone,
    Req: 'static + Send,
    SV::Response: std::marker::Send + Serialize,
    <A as Ack<Req, SV::Response, Cdc>>::Context: Send + Clone,
    <A as Ack<Req, SV::Response, Cdc>>::Context: 'static,
    Ctx: Clone,
{
    type Response = SV::Response;
    type Error = NovaRuntimeError;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(
        &mut self,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), Self::Error>> {
        self.service
            .poll_ready(cx)
            .map_err(|e| NovaRuntimeError::Failed(Arc::new(e.into())))
    }

    fn call(&mut self, request: TaskRequest<Req, Ctx>) -> Self::Future {
        let mut ack = self.ack.clone();
        let ctx = request.parts.context.clone();
        // let attempt = request.parts.attemptmpt.clone();
        let task_id = request.parts.task_id.clone();
        let fut = self.service.call(request);
        let fut_with_ack = async move {
            let res = fut.await.map_err(|err| {
                let e: BoxDynError = err.into();
                if let Some(custom_error) = e.downcast_ref::<NovaRuntimeError>() {
                    return custom_error.clone();
                }
                NovaRuntimeError::Failed(Arc::new(e))
            });
            let response = TaskResult {
                inner: res,
                task_id,
                _priv: (),
            };
            if let Err(e) = ack.ack(&ctx, &response).await {
                tracing::error!("Acknowledgement Failed: {}", e);
            }
            response.inner
        };
        fut_with_ack.boxed()
    }
}