--[[
调度定时任务

这个脚本用于将任务调度到未来的某个时间点执行。
它支持延迟执行、定时任务等场景，是任务调度系统的核心功能。

参数说明：
KEYS[1]: 任务数据哈希表 (hash) - 存储任务ID到任务数据的映射
KEYS[2]: 定时任务集合 (sorted set) - 存储按执行时间排序的定时任务

ARGV[1]: 任务ID - 要调度的任务标识
ARGV[2]: 序列化的任务数据 - 包含任务执行所需的所有信息
ARGV[3]: 调度时间戳 - 任务应该执行的时间点

返回值：
1 - 任务成功调度（新任务）
0 - 任务已存在，更新了调度时间
--]]

-- 先删除可能存在的旧任务数据，确保数据一致性
-- 这样可以处理任务重新调度的情况
redis.call("HDEL", KEYS[1], ARGV[1])

-- 使用 hsetnx 设置任务数据
-- 由于上面已经删除了旧数据，这里总是会成功设置
local set = redis.call("hsetnx", KEYS[1], ARGV[1], ARGV[2])

-- 将任务添加到定时任务集合，使用调度时间作为分数
-- 这样任务会按照执行时间排序，便于定时调度器处理
redis.call("zadd", KEYS[2], ARGV[3], ARGV[1])

-- 返回设置结果
return set
