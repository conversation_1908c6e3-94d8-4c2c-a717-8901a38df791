--[[
标记任务完成

这个脚本用于标记任务已成功完成。它将任务从执行中状态移动到已完成状态，
并保存任务的执行结果，用于后续的查询和统计。

参数说明：
KEYS[1]: 当前消费者的执行中任务集合 (set) - 存储该工作者正在处理的任务 inflight
KEYS[2]: 已完成任务集合 (sorted set) - 存储已完成的任务，按完成时间排序 done
KEYS[3]: 任务数据哈希表 (hash) - 存储任务相关数据的基础键 data

ARGV[1]: 任务ID - 要标记为完成的任务标识
ARGV[2]: 当前时间戳 - 任务完成的时间
ARGV[3]: 任务执行结果 - 任务的输出或返回值

返回值：
true - 任务成功标记为完成
false - 任务不在执行中状态（可能已完成或不存在）
--]]

-- 从当前消费者的执行中任务集合中移除该任务
-- 使用 srem 确保任务确实在该消费者的执行列表中
local removed = redis.call("srem", KEYS[1], ARGV[1])

-- 定义结果存储的命名空间后缀
local ns = "::result"

-- 如果任务确实从执行中集合移除成功
if removed == 1 then
  -- 将任务添加到已完成任务集合，使用完成时间作为分数
  -- 这样可以按时间顺序查询已完成的任务
  redis.call("zadd", KEYS[2], ARGV[2], ARGV[1])

  -- 保存任务的执行结果到专门的结果哈希表
  -- 使用命名空间分离任务数据和结果数据
  -- 注意：使用 hset 替代已废弃的 hmset 命令
  redis.call("hset", KEYS[3].. ns, ARGV[1], ARGV[3] )

  return true
end

-- 任务不在执行中状态，返回失败
return false
