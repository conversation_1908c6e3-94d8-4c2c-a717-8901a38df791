[package]
name = "nova-lab"
version = "0.1.0"
edition = "2024"

[dependencies]
nova-core.workspace = true
tokio.workspace = true
tokio-util.workspace = true
tokio-stream.workspace = true
tracing.workspace = true
tracing-bunyan-formatter.workspace = true
tracing-log.workspace = true
tracing-opentelemetry.workspace = true
opentelemetry-otlp.workspace = true
opentelemetry_sdk.workspace = true
opentelemetry = { version = "0.27.1", features = ["trace"] }
tracing-subscriber.workspace = true
tracing-appender.workspace = true
thiserror.workspace = true
anyhow.workspace = true
serde.workspace = true
serde_json.workspace = true
dotenvy.workspace = true
envconfig.workspace = true
http.workspace = true
strum.workspace = true
reqwest.workspace = true
axum.workspace = true
tower.workspace = true
tower-http.workspace = true
uuid.workspace = true
chrono.workspace = true
nova-runtime.workspace = true
nova-runtime-redis.workspace = true
fred = { version = "10.1.0", features = ["i-scripts"] }

[workspace]
members = [
    "members/nova-core", "members/nova-runtime", "members/nova-runtime-redis",
]


[workspace.dependencies]
nova-core = { version = "0.1.0", path = "./members/nova-core" }
nova-runtime = { version = "0.1.0", path = "./members/nova-runtime" }
nova-runtime-redis = { version = "0.1.0", path = "./members/nova-runtime-redis" }
serde = { version = "1.0.218", features = ["derive"] } #serderslock = 0.6.0
tokio = { version = "1.43", features = ["full"] }
tokio-util = { version = "0.7.13", features = ["full"] }
tokio-stream = { version = "0.1.17", features = ["full"] }
prost = "0.13.5"
tonic = "0.12.3"
tracing = "0.1"
tracing-bunyan-formatter = "0.3.9"
tracing-log = "0.2.0"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "json"] }
tracing-appender = "0.2.3"
opentelemetry = { version = "0.27.1", features = ["trace"] }
opentelemetry-otlp = "0.27.0"
tracing-opentelemetry = "0.28.0"
opentelemetry_sdk = { version = "0.27.1", features = ["rt-tokio", "trace"] }
thiserror = "2.0.11"
serde_json = "1.0"
serde_json_any_key = "2.0.0"
serde_variant = "0.1.3"
serde-aux = "4.6.0"
anyhow = "1.0.96"
bytes = "1.10.0"
rand = "0.9.0"
lazy_static = "1.5.0"
num_cpus = "1.16"
config = "0.15.8"
parking_lot = "0.12"
dirs = "6.0.0"
trait-variant = "0.1.2"
local-ip-address = "0.6.3"
sysinfo = "0.33.1"
cheetah-string = { version = "0.1.6", features = ["serde", "bytes"] }
flate2 = "1.0.35"
dashmap = "6.1.0"
strum = { version = "0.27.1", features = ["derive"] }
axum-auth = "0.8"
hyper = "1.6.0"
axum = { version = "0.8.1", features = ["macros", "tracing"] }
chrono = { version = "0.4.38", features = ["serde"] }
tower = { version = "0.5.2", features = [
    "filter",
    "limit", # 并发限制
    "timeout", # 超时
    "retry", # 重试
    "buffer", # 缓冲
    "util", # 工具函数
] }
tower-http = { version = "0.6.2", features = [
    "compression-full",
    "trace",
    "limit",
    "cors",
    "add-extension",
    "set-header",
    "timeout",
] }
regex = "1.11.1"
reqwest = { version = "0.12.12", features = ["json", "stream", "multipart"] }
async-trait = "0.1.87"
random = "0.14.0"
redis = { version = "0.32.5", features = ["cluster"] }
memmap2 = "0.9.5"
rslock = "0.6.0"
zookeeper = "0.8.0"
crossbeam = "0.8"
dotenvy = "0.15.7"
envconfig = "0.11.0"
http = "1.1.0"
uuid = { version = "1.18.0", features = ["v4", "serde"] }
futures = { version = "0.3.31", features = ["async-await"] }
pin-project-lite = "0.2.16"
ulid = { version = "1.2.1", default-features = false, features = ["std"] }
futures-timer = { version = "3.0.3" }
