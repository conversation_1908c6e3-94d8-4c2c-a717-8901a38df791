use std::sync::Arc;
use anyhow::{anyhow, Result};
use axum::Router;
use tokio::net::TcpListener;
use tracing::info;
use nova_runtime_redis::RedisStorage;
use crate::{router, Email, NovaConfig, QueueManager};

#[derive(Clone)]
pub struct Server {
    pub state: Arc<AppState>,
}

#[derive(Clone)]
pub struct AppState {
    pub config: NovaConfig,
    pub queue_manager: Arc<QueueManager>,
    pub redis_storage: RedisStorage<Email>,
}

impl Server {
    pub async fn init(config: NovaConfig, redis_storage: RedisStorage<Email>) -> Self {
        let queue_manager = Arc::new(QueueManager::new());
        Self {
            state: Arc::new(AppState {
                config,
                queue_manager,
                redis_storage
            }),
        }
    }


    pub async fn run(&self, axum_shutdown_tx: tokio::sync::broadcast::Sender<()>) -> Result<()> {
        let app = router::get_router(self.state.clone()).await;
        let app: Router<()> = app.with_state(self.state.clone());
        info!("Api server listening on {}", self.state.config.server.server_address);
        let tcp_listener = TcpListener::bind(&self.state.config.server.server_address).await?;

        axum::serve(tcp_listener, app.into_make_service())
            .with_graceful_shutdown(async move{
                let mut rx = axum_shutdown_tx.subscribe();
                let _ = rx.recv().await;
                info!("  shutdown app server ...");
            })
            .await
            .map_err(|e| anyhow!("Server error: {}", e))
    }
}
