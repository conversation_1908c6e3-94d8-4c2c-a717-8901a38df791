--!df flags=allow-undeclared-keys
--[[
获取任务队列统计信息

这个脚本用于收集任务队列系统的各种统计数据，包括不同状态的任务数量。
这些统计信息对于监控系统健康状况和性能分析非常重要。

注意：上面的 --!df 标志是为 DragonflyDB 启用未声明键访问。
这是必需的，因为在循环中会动态生成键名来统计正在运行的任务。
虽然 Redis 认为访问未声明的键是不良实践，但在这种统计场景下是必要的。

参数说明：
KEYS[1]: 待处理任务列表 (list) - 等待被工作者处理的任务队列
KEYS[2]: 活跃消费者集合 (sorted set) - 存储所有活跃工作者信息
KEYS[3]: 死亡任务集合 (sorted set) - 存储被终止的任务
KEYS[4]: 失败任务集合 (sorted set) - 存储执行失败的任务
KEYS[5]: 成功任务集合 (sorted set) - 存储执行成功的任务

返回值：
返回包含各状态任务数量的数组：[待处理, 运行中, 死亡, 失败, 成功]
--]]

-- 获取各个键的引用，提高代码可读性
local pending_jobs_set = KEYS[1]    -- 待处理任务列表
local consumer_set = KEYS[2]        -- 活跃消费者集合
local dead_jobs_set = KEYS[3]       -- 死亡任务集合
local failed_jobs_set = KEYS[4]     -- 失败任务集合
local success_jobs_set = KEYS[5]    -- 成功任务集合

-- 获取所有活跃的消费者列表
local consumers = redis.call("zrangebyscore", consumer_set, 0, "+inf")

-- 统计所有消费者正在执行的任务总数
local running_count = 0
for _, consumer_inflight_set in ipairs(consumers) do
    -- 每个消费者都有自己的执行中任务集合
    -- 累加所有消费者的执行中任务数量
    running_count = running_count + redis.call("SCARD", consumer_inflight_set)
end

-- 统计各状态的任务数量
local pending_count = redis.call('LLEN', pending_jobs_set)   -- 待处理任务数
local dead_count = redis.call('ZCARD', dead_jobs_set)        -- 死亡任务数
local failed_count = redis.call('ZCARD', failed_jobs_set)    -- 失败任务数
local success_count = redis.call('ZCARD', success_jobs_set)  -- 成功任务数

-- 返回统计结果数组：[待处理, 运行中, 死亡, 失败, 成功]
return { pending_count, running_count, dead_count, failed_count, success_count }
