--[[
清理已完成任务的数据

这个脚本用于清理已完成任务的相关数据，释放 Redis 内存空间。
它会删除已完成任务的数据记录，并清空已完成任务列表。

参数说明：
KEYS[1]: 已完成任务列表 (sorted set) - 存储已完成任务的ID列表
KEYS[2]: 任务数据哈希表 (hash) - 存储任务ID到任务数据的映射

返回值：
返回实际清理的任务数据条目数量
--]]

-- 获取键的引用，提高代码可读性
local done_list_key = KEYS[1]    -- 已完成任务列表键
local data_hash = KEYS[2]        -- 任务数据哈希表键

-- 获取所有已完成任务的ID列表
-- 使用 ZRANGE 获取整个有序集合中的所有成员
local done_list_ids = redis.call('ZRANGE', done_list_key, 0, -1)

-- 初始化清理计数器
local removed_items_count = 0

-- 遍历每个已完成的任务ID
for _, id in ipairs(done_list_ids) do
    -- 检查该任务的数据是否还存在于数据哈希表中
    local is_member = redis.call('HEXISTS', data_hash, id)

    -- 如果任务数据存在，则删除它
    if is_member == 1 then
        -- 从任务数据哈希表中删除该任务的数据
        redis.call('HDEL', data_hash, id)

        -- 增加清理计数
        removed_items_count = removed_items_count + 1
    end
end

-- 清空已完成任务列表
-- 删除整个已完成任务集合，释放内存
redis.call('DEL', done_list_key)

-- 返回实际清理的数据条目数量
return removed_items_count
