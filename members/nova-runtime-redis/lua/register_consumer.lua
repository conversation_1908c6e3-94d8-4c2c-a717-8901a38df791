--[[
注册消费者/工作者

这个脚本用于将工作者注册到活跃消费者列表中，并更新其最后活跃时间。
这是工作者生命周期管理的重要组成部分，用于跟踪哪些工作者在线。

参数说明：
KEYS[1]: 活跃消费者集合 (sorted set) - 存储所有活跃工作者及其最后活跃时间 consumer

ARGV[1]: 当前时间戳 - 工作者的注册/心跳时间
ARGV[2]: 消费者标识 - 唯一标识该工作者的字符串（通常是执行中任务集合的键名）

返回值：
true - 消费者成功注册或更新心跳
--]]

-- 将消费者添加到活跃消费者集合，使用当前时间作为分数
-- 如果消费者已存在，会更新其最后活跃时间
-- 这样可以通过分数（时间戳）来识别长时间未活跃的工作者
redis.call("zadd", KEYS[1], ARGV[1], ARGV[2])

-- 返回成功标志
return true
