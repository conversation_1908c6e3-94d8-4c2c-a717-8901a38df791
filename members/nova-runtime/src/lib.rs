use crate::codec::NoopCodec;
use crate::error::NovaRuntimeError;
use crate::task::{Parts, TaskId, TaskRequest, TaskSource};
use crate::worker::{<PERSON><PERSON>, Worker};
use futures::future::BoxFuture;
use futures::Stream;
use std::error::Error;
use std::pin::Pin;
use crate::worker::Context;
use std::time::Duration;
use tower::layer::util::Identity;

pub mod coordinator;
pub mod worker;
pub mod task;
pub mod error;
pub mod codec;
pub mod layers;
pub mod service_fn;
pub mod mq;

pub type BoxDynError = Box<dyn Error + 'static + Send + Sync>;
pub type BoxStream<'a, T> = Pin<Box<dyn Stream<Item = T> + Send + 'a>>;
pub type TaskRequestFuture<T> = BoxFuture<'static, T>;

pub type TaskRequestStream<T> = BoxStream<'static, Result<Option<T>, NovaRuntimeError>>;



/// Common imports
pub mod prelude {
    pub use crate::layers::WorkerBuilderExt;
    pub use crate::{
        codec::Codec,
        coordinator::Coordinator,
        error::NovaRuntimeError,
        layers::{AddExtension, SharedData},
        service_fn::{service_fn, FromRequest, ServiceFn},
        task::task_id::TaskId,
        task::Extensions,
        task::IntoResponse,
        task::State,
        task::TaskSource,
        task::TaskSourceStream,
        task::task_request::TaskRequest,
        mq::Stat,
        mq::TaskSourceExpose,
        mq::WorkerState,
        worker::{Context, Event, Ready, Worker},
        worker::{Poller, PollerState},
        worker::{WorkerBuilder, WorkerFactory, WorkerFactoryFn},
    };
}

impl<T, Ctx> TaskSource<TaskRequest<T, Ctx>> for TaskRequestStream<TaskRequest<T, Ctx>> {
    type Stream = Self;

    type Layer = Identity;

    type Codec = NoopCodec<TaskRequest<T, Ctx>>;

    fn poll(self, _worker: &Worker<Context>) -> Poller<Self::Stream> {
        Poller {
            stream: self,
            heartbeat: Box::pin(futures::future::pending()),
            layer: Identity::new(),
            _priv: (),
        }
    }
}

pub async fn sleep(duration: std::time::Duration) {
    futures_timer::Delay::new(duration).await;
}

pub mod interval {
    use std::fmt;
    use std::future::Future;
    use std::pin::Pin;
    use std::task::{Context, Poll};
    use std::time::Duration;

    use futures::future::BoxFuture;
    use futures::Stream;

    use crate::sleep;
    /// Creates a new stream that yields at a set interval.
    pub fn interval(duration: Duration) -> Interval {
        Interval {
            timer: Box::pin(sleep(duration)),
            interval: duration,
        }
    }

    /// A stream representing notifications at fixed interval
    #[must_use = "streams do nothing unless polled or .awaited"]
    pub struct Interval {
        timer: BoxFuture<'static, ()>,
        interval: Duration,
    }

    impl fmt::Debug for Interval {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_struct("Interval")
                .field("interval", &self.interval)
                .field("timer", &"a future represented `apalis_core::sleep`")
                .finish()
        }
    }

    impl Stream for Interval {
        type Item = ();

        fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
            match Pin::new(&mut self.timer).poll(cx) {
                Poll::Ready(_) => {}
                Poll::Pending => return Poll::Pending,
            };
            let interval = self.interval;
            let fut = std::mem::replace(&mut self.timer, Box::pin(sleep(interval)));
            drop(fut);
            Poll::Ready(Some(()))
        }
    }
}



/// Represents a [Storage] that can persist a request.
pub trait Storage: TaskSource<TaskRequest<Self::Job, Self::Context>> {
    /// The type of job that can be persisted
    type Job;

    /// The error produced by the storage
    type Error;

    /// This is the type that storages store as the metadata related to a job
    type Context: Default;

    /// The format that the storage persists the jobs usually `Vec<u8>`
    type Compact;

    /// Pushes a job to a storage
    fn push(
        &mut self,
        job: Self::Job,
    ) -> impl Future<Output = Result<Parts<Self::Context>, Self::Error>> + Send {
        self.push_request(TaskRequest::new(job))
    }

    /// Pushes a constructed request to a storage
    fn push_request(
        &mut self,
        req: TaskRequest<Self::Job, Self::Context>,
    ) -> impl Future<Output = Result<Parts<Self::Context>, Self::Error>> + Send;

    /// Pushes a constructed request to a storage
    fn push_raw_request(
        &mut self,
        req: TaskRequest<Self::Compact, Self::Context>,
    ) -> impl Future<Output = Result<Parts<Self::Context>, Self::Error>> + Send;

    /// Push a job with defaults into the scheduled set
    fn schedule(
        &mut self,
        job: Self::Job,
        on: i64,
    ) -> impl Future<Output = Result<Parts<Self::Context>, Self::Error>> + Send {
        self.schedule_request(TaskRequest::new(job), on)
    }

    /// Push a request into the scheduled set
    fn schedule_request(
        &mut self,
        request: TaskRequest<Self::Job, Self::Context>,
        on: i64,
    ) -> impl Future<Output = Result<Parts<Self::Context>, Self::Error>> + Send;

    /// Return the number of pending jobs from the queue
    fn len(&mut self) -> impl Future<Output = Result<i64, Self::Error>> + Send;

    /// Fetch a job given an id
    fn fetch_by_id(
        &mut self,
        job_id: &TaskId,
    ) -> impl Future<Output = Result<Option<TaskRequest<Self::Job, Self::Context>>, Self::Error>> + Send;

    /// Update a job details
    fn update(
        &mut self,
        job: TaskRequest<Self::Job, Self::Context>,
    ) -> impl Future<Output = Result<(), Self::Error>> + Send;

    /// Reschedule a job
    fn reschedule(
        &mut self,
        job: TaskRequest<Self::Job, Self::Context>,
        wait: Duration,
    ) -> impl Future<Output = Result<(), Self::Error>> + Send;

    /// Returns true if there is no jobs in the storage
    fn is_empty(&mut self) -> impl Future<Output = Result<bool, Self::Error>> + Send;

    /// Vacuum the storage, removes done and killed jobs
    fn vacuum(&mut self) -> impl Future<Output = Result<usize, Self::Error>> + Send;
}