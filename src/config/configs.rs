use envconfig::Envconfig;
use crate::Environment;

#[derive(Envconfig,Debug,Clone)]
pub struct NovaConfig {
    #[envconfig(nested)]
    pub server: NovaServerConfig,

    #[envconfig(nested)]
    pub trace: NovaTraceConfig,

    #[envconfig(from = "ENVIRONMENT", default = "development")]
    pub environment: Environment,

    #[envconfig(from = "REDIS_URL", default = "redis://127.0.0.1:6379/")]
    pub redis_url: String,


}


#[derive(Envconfig,Debug,Clone)]
pub struct NovaServerConfig {

    #[envconfig(from = "SERVER_ADDRESS",default="0.0.0.0:8888")]
    pub server_address: String,
}

#[derive(Envconfig,Debug,Clone)]
pub struct NovaTraceConfig {
    #[envconfig(from = "SERVER_PORT",default="8888")]
    pub server_port: u16,

    #[envconfig(from = "SERVER_LOG_LEVEL",default="info")]
    pub server_log_level: String,

    #[envconfig(from = "TELEMETRY_ENDPOINT")]
    pub telemetry_endpoint: Option<String>,
}
