[package]
name = "nova-runtime-redis"
version = "0.1.0"
edition = "2024"

[dependencies]
nova-runtime.workspace = true
redis = { version = "0.32", default-features = false, features = [
    "script",
    "aio",
    "connection-manager",
] }
tokio = { version = "1", features = ["rt", "net", "time"], optional = true }
chrono.workspace = true
futures.workspace = true
thiserror.workspace = true
serde.workspace = true
tracing.workspace = true
url = "2.5"
log = "0.4.28"
fred = { version = "10.1.0", features = ["sha-1","tracing","tracing-futures"] }
anyhow.workspace = true

[features]
default = ["tokio-comp"]
tokio-comp = ["tokio", "tokio/net", "redis/tokio-comp"]