use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use tracing::{info, warn, error};
use serde_json::json;
use crate::queue::{QueueManager, Task, TaskType, TaskStatus};

pub struct Worker {
    id: String,
    queue_manager: Arc<QueueManager>,
}

impl Worker {
    pub fn new(id: String, queue_manager: Arc<QueueManager>) -> Self {
        Self { id, queue_manager }
    }

    /// 启动工作线程
    pub async fn start(&self) {
        info!("Worker {} started", self.id);
        
        loop {
            // 尝试获取任务
            if let Some(mut task) = self.queue_manager.get_next_task().await {
                info!("Worker {} picked up task {}", self.id, task.id);
                
                // 标记任务为运行中
                task.start();
                self.queue_manager.update_task(&task.id, task.clone()).await;

                // 处理任务
                let result = self.process_task(&mut task).await;
                
                // 更新任务状态
                match result {
                    Ok(result_data) => {
                        task.complete(result_data);
                        info!("Worker {} completed task {}", self.id, task.id);
                    }
                    Err(error) => {
                        task.fail(error.to_string());
                        error!("Worker {} failed to process task {}: {}", self.id, task.id, error);
                        
                        // 如果可以重试，将任务重新加入队列
                        if task.can_retry() {
                            warn!("Task {} will be retried", task.id);
                            self.queue_manager.retry_task(&task.id).await;
                        }
                    }
                }
                
                self.queue_manager.update_task(&task.id.clone(), task).await;
            } else {
                // 没有任务时等待通知
                self.queue_manager.wait_for_task().await;
            }
        }
    }

    /// 处理具体任务
    async fn process_task(&self, task: &mut Task) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        match &task.task_type {
            TaskType::EmailSend => self.process_email_task(task).await,
            TaskType::DataProcess => self.process_data_task(task).await,
            TaskType::FileUpload => self.process_file_upload_task(task).await,
            TaskType::Custom(custom_type) => self.process_custom_task(task, custom_type).await,
        }
    }

    /// 处理邮件发送任务
    async fn process_email_task(&self, task: &Task) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        info!("Processing email task: {}", task.id);
        
        // 模拟邮件发送处理时间
        sleep(Duration::from_secs(2)).await;
        
        // 从payload中提取邮件信息
        let to = task.payload.get("to")
            .and_then(|v| v.as_str())
            .ok_or("Missing 'to' field in email task")?;
        let subject = task.payload.get("subject")
            .and_then(|v| v.as_str())
            .ok_or("Missing 'subject' field in email task")?;
        let _body = task.payload.get("body")
            .and_then(|v| v.as_str())
            .ok_or("Missing 'body' field in email task")?;

        // 这里可以集成真实的邮件发送服务
        // 比如 SMTP, SendGrid, AWS SES 等
        
        info!("Email sent to: {}, subject: {}", to, subject);
        
        Ok(json!({
            "status": "sent",
            "to": to,
            "subject": subject,
            "sent_at": chrono::Utc::now().to_rfc3339()
        }))
    }

    /// 处理数据处理任务
    async fn process_data_task(&self, task: &Task) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        info!("Processing data task: {}", task.id);
        
        // 模拟数据处理时间
        sleep(Duration::from_secs(3)).await;
        
        let data = task.payload.get("data")
            .ok_or("Missing 'data' field in data processing task")?;
        let operation = task.payload.get("operation")
            .and_then(|v| v.as_str())
            .unwrap_or("default");

        // 模拟数据处理逻辑
        let processed_data = match operation {
            "sum" => {
                if let Some(numbers) = data.as_array() {
                    let sum: f64 = numbers.iter()
                        .filter_map(|v| v.as_f64())
                        .sum();
                    json!({"result": sum, "operation": "sum"})
                } else {
                    return Err("Data is not an array for sum operation".into());
                }
            }
            "count" => {
                if let Some(array) = data.as_array() {
                    json!({"result": array.len(), "operation": "count"})
                } else {
                    json!({"result": 1, "operation": "count"})
                }
            }
            _ => {
                json!({"result": "processed", "operation": operation, "original_data": data})
            }
        };

        Ok(json!({
            "status": "processed",
            "data": processed_data,
            "processed_at": chrono::Utc::now().to_rfc3339()
        }))
    }

    /// 处理文件上传任务
    async fn process_file_upload_task(&self, task: &Task) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        info!("Processing file upload task: {}", task.id);
        
        // 模拟文件上传处理时间
        sleep(Duration::from_secs(5)).await;
        
        let filename = task.payload.get("filename")
            .and_then(|v| v.as_str())
            .ok_or("Missing 'filename' field in file upload task")?;
        let file_size = task.payload.get("file_size")
            .and_then(|v| v.as_u64())
            .unwrap_or(0);

        // 这里可以集成真实的文件上传服务
        // 比如 AWS S3, Google Cloud Storage, 本地文件系统等
        
        info!("File uploaded: {}, size: {} bytes", filename, file_size);
        
        Ok(json!({
            "status": "uploaded",
            "filename": filename,
            "file_size": file_size,
            "upload_url": format!("https://storage.example.com/files/{}", filename),
            "uploaded_at": chrono::Utc::now().to_rfc3339()
        }))
    }

    /// 处理自定义任务
    async fn process_custom_task(&self, task: &Task, custom_type: &str) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        info!("Processing custom task: {} of type: {}", task.id, custom_type);
        
        // 模拟自定义任务处理时间
        sleep(Duration::from_secs(1)).await;
        
        // 这里可以根据 custom_type 实现不同的处理逻辑
        match custom_type {
            "webhook" => {
                let url = task.payload.get("url")
                    .and_then(|v| v.as_str())
                    .ok_or("Missing 'url' field in webhook task")?;
                
                // 模拟 webhook 调用
                info!("Calling webhook: {}", url);
                
                Ok(json!({
                    "status": "webhook_called",
                    "url": url,
                    "response_code": 200,
                    "called_at": chrono::Utc::now().to_rfc3339()
                }))
            }
            "notification" => {
                let message = task.payload.get("message")
                    .and_then(|v| v.as_str())
                    .ok_or("Missing 'message' field in notification task")?;
                
                info!("Sending notification: {}", message);
                
                Ok(json!({
                    "status": "notification_sent",
                    "message": message,
                    "sent_at": chrono::Utc::now().to_rfc3339()
                }))
            }
            _ => {
                Ok(json!({
                    "status": "processed",
                    "custom_type": custom_type,
                    "payload": task.payload,
                    "processed_at": chrono::Utc::now().to_rfc3339()
                }))
            }
        }
    }
}
