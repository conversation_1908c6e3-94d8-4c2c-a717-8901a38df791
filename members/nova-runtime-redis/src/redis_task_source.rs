use std::any::type_name;
use std::fmt::Debug;
use std::fmt::{self};
use std::io;
use std::marker::PhantomData;
use std::num::TryFromIntError;
use std::task::Context;
use std::time::Duration;
use std::time::SystemTime;

use chrono::DateTime;
use chrono::Utc;
use fred::bytes_utils::Str;
use fred::error::Error as RedisError;
use fred::error::ErrorKind;
use fred::prelude::{Client, Value};
use fred::prelude::HashesInterface;
use fred::prelude::ListInterface;
use fred::prelude::LuaInterface;
use fred::prelude::SetsInterface;
use fred::prelude::SortedSetsInterface;
use fred::types::scripts::Library;
use futures::FutureExt;
use futures::SinkExt;
use futures::StreamExt;
use futures::TryFutureExt;
use futures::channel::mpsc::SendError;
use futures::channel::mpsc::Sender;
use futures::channel::mpsc::{self};
use futures::select;
use log::info;
use log::warn;
use nova_runtime::BoxDynError;
use nova_runtime::Storage;
use nova_runtime::TaskRequestStream;
use nova_runtime::codec::Codec;
use nova_runtime::codec::JsonCodec;
use nova_runtime::error::NovaRuntimeError;
use nova_runtime::layers::Ack;
use nova_runtime::layers::AckLayer;
use nova_runtime::service_fn::FromRequest;
use nova_runtime::task::Parts;
use nova_runtime::task::TaskId;
use nova_runtime::task::TaskRequest;
use nova_runtime::task::TaskResult;
use nova_runtime::task::TaskSource;
use nova_runtime::task::TaskSourceStream;
use nova_runtime::worker::Event;
use nova_runtime::worker::Poller;
use nova_runtime::worker::PollerState;
use nova_runtime::worker::Worker;
use redis::IntoConnectionInfo;
use redis::Script;
// use redis::Value;
use redis::aio::ConnectionLike;
use redis::aio::ConnectionManager;
use serde::Deserialize;
use serde::Serialize;
use serde::de::DeserializeOwned;

/// 运行时队列
const ACTIVE_JOBS_LIST: &str = "{queue}:active";

/// 消费者集合
const CONSUMERS_SET: &str = "{queue}:consumers";
/// 死亡任务集合
const DEAD_JOBS_SET: &str = "{queue}:dead";
/// 完成任务集合
const DONE_JOBS_SET: &str = "{queue}:done";
/// 失败任务集合
const FAILED_JOBS_SET: &str = "{queue}:failed";
/// 工作者正在处理任务集合
const INFLIGHT_JOB_SET: &str = "{queue}:inflight";
/// 任务数据
const JOB_DATA_HASH: &str = "{queue}:data";
/// 计划任务集合
const SCHEDULED_JOBS_SET: &str = "{queue}:scheduled";
/// 信号列表
const SIGNAL_LIST: &str = "{queue}:signal";

/// Represents redis key names for various components of the RedisStorage.
///
/// This struct defines keys used in Redis to manage jobs and their lifecycle in the storage.
/// 队列信息
#[derive(Clone, Debug)]
pub struct RedisQueueInfo {
    /// Key for the list of currently active jobs.
    pub active_jobs_list: String,

    /// Key for the set of active consumers.
    pub consumers_set: String,

    /// Key for the set of jobs that are no longer retryable.
    pub dead_jobs_set: String,

    /// Key for the set of jobs that have completed successfully.
    pub done_jobs_set: String,

    /// Key for the set of jobs that have failed.
    pub failed_jobs_set: String,

    /// Key for the set of jobs that are currently being processed.
    pub inflight_jobs_set: String,

    /// Key for the hash storing data for each job.
    pub job_data_hash: String,

    /// Key for the set of jobs scheduled for future execution.
    pub scheduled_jobs_set: String,

    /// Key for the list used for signaling and communication between consumers and producers.
    pub signal_list: String,
}

/// redis脚本
#[derive(Clone, Debug)]
pub struct RedisScript {
    pub done_job: String,
    pub enqueue_scheduled: String,
    pub get_jobs: String,
    pub kill_job: String,
    pub push_job: String,
    pub re_enqueue_active: String,
    pub re_enqueue_orphaned: String,
    pub register_consumer: String,
    pub retry_job: String,
    pub schedule_job: String,
    pub vacuum: String,
    pub stats: String,
}

/// The context for a redis storage job
/// 任务上下文
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RedisContext {
    max_attempts: usize,
    lock_by: Option<String>,
    run_at: Option<SystemTime>,
}

impl Default for RedisContext {
    fn default() -> Self {
        Self {
            max_attempts: 5,
            lock_by: None,
            run_at: None,
        }
    }
}

/// 从请求中获取上下文
impl<Req> FromRequest<TaskRequest<Req, RedisContext>> for RedisContext {
    fn from_request(req: &TaskRequest<Req, RedisContext>) -> Result<Self, NovaRuntimeError> {
        Ok(req.parts.context.clone())
    }
}

/// Errors that can occur while polling a Redis backend.
#[derive(thiserror::Error, Debug)]
pub enum RedisPollError {
    /// Error during a keep-alive heartbeat.
    #[error("KeepAlive heartbeat encountered an error: `{0}`")]
    KeepAliveError(RedisError),

    /// Error during enqueueing scheduled tasks.
    #[error("EnqueueScheduled heartbeat encountered an error: `{0}`")]
    EnqueueScheduledError(RedisError),

    /// Error during polling for the next task or message.
    #[error("PollNext heartbeat encountered an error: `{0}`")]
    PollNextError(RedisError),

    /// Error during enqueueing tasks for worker consumption.
    #[error("Enqueue for worker consumption encountered an error: `{0}`")]
    EnqueueError(SendError),

    /// Error during acknowledgment of tasks.
    #[error("Ack heartbeat encountered an error: `{0}`")]
    AckError(RedisError),

    /// Error during re-enqueuing orphaned tasks.
    #[error("ReenqueueOrphaned heartbeat encountered an error: `{0}`")]
    ReenqueueOrphanedError(RedisError),

    /// Internal error
    #[error("Internal error: `{0}`")]
    InternalError(#[from] anyhow::Error),
}

/// Config for a [RedisStorage]
#[derive(Clone, Debug)]
pub struct Config {
    poll_interval: Duration,
    buffer_size: usize,
    keep_alive: Duration,
    enqueue_scheduled: Duration,
    re_enqueue_orphaned_after: Duration,
    namespace: String,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            poll_interval: Duration::from_millis(100),
            buffer_size: 10,
            keep_alive: Duration::from_secs(30),
            enqueue_scheduled: Duration::from_secs(30),
            re_enqueue_orphaned_after: Duration::from_secs(300),
            namespace: String::from("nova_redis"),
        }
    }
}

impl Config {
    /// Get the interval of polling
    pub fn get_poll_interval(&self) -> &Duration {
        &self.poll_interval
    }

    /// Get the number of jobs to fetch
    pub fn get_buffer_size(&self) -> usize {
        self.buffer_size
    }

    /// get the keep live rate
    pub fn get_keep_alive(&self) -> &Duration {
        &self.keep_alive
    }

    /// get the enqueued setting
    pub fn get_enqueue_scheduled(&self) -> &Duration {
        &self.enqueue_scheduled
    }

    /// get the namespace
    pub fn get_namespace(&self) -> &String {
        &self.namespace
    }

    /// get the poll interval
    pub fn set_poll_interval(mut self, poll_interval: Duration) -> Self {
        self.poll_interval = poll_interval;
        self
    }

    /// set the buffer setting
    pub fn set_buffer_size(mut self, buffer_size: usize) -> Self {
        self.buffer_size = buffer_size;
        self
    }

    /// set the keep-alive setting
    pub fn set_keep_alive(mut self, keep_alive: Duration) -> Self {
        self.keep_alive = keep_alive;
        self
    }

    /// get the enqueued setting
    pub fn set_enqueue_scheduled(mut self, enqueue_scheduled: Duration) -> Self {
        self.enqueue_scheduled = enqueue_scheduled;
        self
    }

    /// 全局前缀
    pub fn set_namespace(mut self, namespace: &str) -> Self {
        self.namespace = namespace.to_string();
        self
    }

    /// 活动任务列表 active
    pub fn active_jobs_list(&self) -> String {
        ACTIVE_JOBS_LIST.replace("{queue}", &self.namespace)
    }

    /// 消费者
    pub fn consumers_set(&self) -> String {
        CONSUMERS_SET.replace("{queue}", &self.namespace)
    }

    /// Returns the Redis key for the set of dead jobs associated with the queue.
    /// The key is dynamically generated using the namespace of the queue.
    ///
    /// # Returns
    /// A `String` representing the Redis key for the dead jobs set.
    pub fn dead_jobs_set(&self) -> String {
        DEAD_JOBS_SET.replace("{queue}", &self.namespace)
    }

    /// 完成任务数据
    pub fn done_jobs_set(&self) -> String {
        DONE_JOBS_SET.replace("{queue}", &self.namespace)
    }

    /// 失败任务数据
    pub fn failed_jobs_set(&self) -> String {
        FAILED_JOBS_SET.replace("{queue}", &self.namespace)
    }

    ///该工作者正在处理的任务
    pub fn inflight_jobs_set(&self) -> String {
        INFLIGHT_JOB_SET.replace("{queue}", &self.namespace)
    }

    /// 任务数据
    pub fn job_data_hash(&self) -> String {
        JOB_DATA_HASH.replace("{queue}", &self.namespace)
    }

    /// Returns the Redis key for the set of scheduled jobs associated with the queue.
    /// The key is dynamically generated using the namespace of the queue.
    ///
    /// # Returns
    /// A `String` representing the Redis key for the scheduled jobs set.
    pub fn scheduled_jobs_set(&self) -> String {
        SCHEDULED_JOBS_SET.replace("{queue}", &self.namespace)
    }

    /// 信号列表 signal
    pub fn signal_list(&self) -> String {
        SIGNAL_LIST.replace("{queue}", &self.namespace)
    }

    /// Gets the re_enqueue_orphaned_after duration.
    pub fn re_enqueue_orphaned_after(&self) -> Duration {
        self.re_enqueue_orphaned_after
    }

    /// Gets a mutable reference to the re_enqueue_orphaned_after.
    pub fn re_enqueue_orphaned_after_mut(&mut self) -> &mut Duration {
        &mut self.re_enqueue_orphaned_after
    }

    /// Occasionally some workers die, or abandon jobs because of panics.
    /// This is the time a task takes before its back to the queue
    ///
    /// Defaults to 5 minutes
    pub fn set_re_enqueue_orphaned_after(mut self, after: Duration) -> Self {
        self.re_enqueue_orphaned_after = after;
        self
    }
}

/// Represents a [Storage] that uses Redis for storage.
pub struct RedisStorage<T, C = JsonCodec<Vec<u8>>> {
    client: Client,
    job_type: PhantomData<T>,
    pub(super) scripts: RedisScript,
    poller_state: PollerState,
    config: Config,
    codec: PhantomData<C>,
}

impl<T, C> Debug for RedisStorage<T, C> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("RedisStorage")
            .field("job_type", &std::any::type_name::<T>())
            .field("scripts", &self.scripts)
            .field("config", &self.config)
            .finish()
    }
}

impl<T, C> Clone for RedisStorage<T, C> {
    fn clone(&self) -> Self {
        Self {
            client: self.client.clone(),
            job_type: PhantomData,
            scripts: self.scripts.clone(),
            poller_state: self.poller_state.clone(),
            config: self.config.clone(),
            codec: self.codec,
        }
    }
}

impl<T: Serialize + DeserializeOwned> RedisStorage<T, JsonCodec<Vec<u8>>> {
    /// 创建一个新的 RedisStorage 实例，并验证 Redis 连接
    pub async fn new(
        redis_client: Client,
        scripts: RedisScript,
    ) -> RedisStorage<T, JsonCodec<Vec<u8>>> {
        Self::new_with_codec::<JsonCodec<Vec<u8>>>(
            redis_client,
            Config::default().set_namespace(type_name::<T>()),
            scripts,
        )
    }

    pub fn new_with_codec<K>(
        client: Client,
        config: Config,
        scripts: RedisScript,
    ) -> RedisStorage<T, K>
    where
        K: Codec + Sync + Send + 'static,
    {
        RedisStorage {
            client,
            job_type: PhantomData,
            poller_state: PollerState::new(),
            config,
            codec: PhantomData::<K>,
            scripts: scripts.clone(),
        }
    }

    /// Get current connection
    pub fn get_connection(&self) -> &Client {
        &self.client
    }

    /// Get the config used by the storage
    pub fn get_config(&self) -> &Config {
        &self.config
    }
}

impl<T, C> TaskSource<TaskRequest<T, RedisContext>> for RedisStorage<T, C>
where
    T: Serialize + DeserializeOwned + Sync + Send + Unpin + 'static,
    C: Codec<Compact = Vec<u8>> + Send + 'static,
{
    type Stream = TaskSourceStream<TaskRequestStream<TaskRequest<T, RedisContext>>>;

    type Layer = AckLayer<Sender<(RedisContext, TaskResult<Vec<u8>>)>, T, RedisContext, C>;

    type Codec = C;

    /// 此方法会轮询 Redis 队列，拉取任务并返回任务流
    fn poll(
        mut self,
        worker: &Worker<nova_runtime::worker::Context>,
    ) -> Poller<Self::Stream, Self::Layer> {
        let (mut tx, rx) = mpsc::channel(self.config.buffer_size);
        let (ack, ack_rx) =
            mpsc::channel::<(RedisContext, TaskResult<Vec<u8>>)>(self.config.buffer_size);

        let layer = AckLayer::new(ack);
        let poller_state = self.poller_state.clone();
        let config = self.config.clone();
        let stream: TaskRequestStream<TaskRequest<T, RedisContext>> = Box::pin(rx);
        let worker = worker.clone();
        // 心跳任务
        let heartbeat = async move {
            // 由于工作者崩溃或长时间无响应而产生的孤儿任务重新放入到队列
            if let Err(e) = self
                .re_enqueue_orphaned((config.buffer_size * 10) as i32, Utc::now())
                .await
            {
                worker.emit(Event::Error(Box::new(
                    RedisPollError::ReenqueueOrphanedError(e),
                )));
            }
            // 重新放入孤儿任务周期
            let mut re_enqueue_orphaned_stm =
                nova_runtime::interval::interval(config.poll_interval).fuse();

            // 心跳周期
            let mut keep_alive_stm = nova_runtime::interval::interval(config.keep_alive).fuse();

            //定时任务
            let mut enqueue_scheduled_stm =
                nova_runtime::interval::interval(config.enqueue_scheduled).fuse();

            // 拉取任务周期
            let mut poll_next_stm = nova_runtime::interval::interval(config.poll_interval).fuse();

            let mut ack_stream = ack_rx.fuse();

            //注册工作者
            if let Err(e) = self.keep_alive(worker.name()).await {
                worker.emit(Event::Error(Box::new(RedisPollError::KeepAliveError(e))));
            }

            loop {
                select! {
                    _ = keep_alive_stm.next() => {
                        if let Err(e) = self.keep_alive(worker.name()).await {
                            worker.emit(Event::Error(Box::new(RedisPollError::KeepAliveError(e))));
                        }
                    }
                    _ = enqueue_scheduled_stm.next() => {
                        if let Err(e) = self.enqueue_scheduled(config.buffer_size).await {
                            worker.emit(Event::Error(Box::new(RedisPollError::EnqueueScheduledError(e))));
                        }
                    }
                    _ = poll_next_stm.next() => {
                        if worker.is_ready() {
                            let res = self.fetch_next(worker.name()).await;
                            match res {
                                Err(e) => {
                                    worker.emit(Event::Error(Box::new(RedisPollError::PollNextError(e))));
                                }
                                Ok(res) => {
                                    for job in res {
                                        if let Err(e) = tx.send(Ok(Some(job))).await {
                                            worker.emit(Event::Error(Box::new(RedisPollError::EnqueueError(e))));
                                        }
                                    }
                                }
                            }
                        } else {
                            continue;
                        }

                    }
                    id_to_ack = ack_stream.next() => {
                        if let Some((ctx, res)) = id_to_ack {
                            if let Err(e) = self.ack(&ctx, &res).await {
                                worker.emit(Event::Error(Box::new(RedisPollError::AckError(e))));
                            }
                        }
                    }
                    _ = re_enqueue_orphaned_stm.next() => {
                        let dead_since = Utc::now()
                            - chrono::Duration::from_std(config.re_enqueue_orphaned_after).unwrap();
                        if let Err(e) = self.re_enqueue_orphaned((config.buffer_size * 10) as i32, dead_since).await {
                            worker.emit(Event::Error(Box::new(RedisPollError::ReenqueueOrphanedError(e))));
                        }
                    }
                }
            }
        };

        //构造可ack的任务流
        Poller::new_with_layer(
            TaskSourceStream::new(stream, poller_state),
            heartbeat.boxed(),
            layer,
        )
    }
}

/// 此方法会处理任务的确认，更新任务状态并返回确认结果
impl<T, C, Res> Ack<T, Res, C> for RedisStorage<T, C>
where
    T: Sync + Send + Serialize + DeserializeOwned + Unpin + 'static,
    C: Codec<Compact = Vec<u8>> + Send + 'static,
    Res: Serialize + Sync + Send + 'static,
{
    type Context = RedisContext;
    type AckError = RedisError;

    /// 任务确认
    async fn ack(&mut self, ctx: &Self::Context, res: &TaskResult<Res>) -> Result<(), RedisError> {
        tracing::info!("ack task: {:?}", res.task_id);
        // let mut task = self
        //     .fetch_by_id(&res.task_id)
        //     .await?
        //     .expect("must be a valid task");

        // 跟新任务状态
        // self.update(task).await?;

        let inflight_set = format!(
            "{}:{}",
            self.config.inflight_jobs_set(),
            ctx.lock_by.clone().unwrap()
        );

        let now: i64 = Utc::now().timestamp();
        let task_id = res.task_id.to_string();
        match &res.inner {
            /// 任务成功
            Ok(success_res) => {
                let done_job = self.scripts.done_job.clone();
                let done_jobs_set = &self.config.done_jobs_set();
                let success_res = C::encode(success_res).map_err(Into::into).unwrap();
                let success_res = String::from_utf8(success_res)?;
                //任务完成，数据处理
                self.client
                    .evalsha(
                        &done_job,
                        &[
                            inflight_set.as_bytes(),
                            done_jobs_set.as_bytes(),
                            self.config.job_data_hash().as_bytes(),
                        ],
                        &[task_id, now.to_string(), success_res],
                    )
                    .await
            }
            /// 任务失败
            Err(e) => match e {
                NovaRuntimeError::Abort(e) => {
                    let worker_id = ctx.lock_by.as_ref().unwrap();
                    self.kill(worker_id, &res.task_id, &e).await
                }
                _ => {
                    if ctx.max_attempts > 1 {
                        let worker_id = ctx.lock_by.as_ref().unwrap();
                        self.retry(worker_id, &res.task_id).await.map(|_| ())
                    } else {
                        let worker_id = ctx.lock_by.as_ref().unwrap();

                        self.kill(
                            worker_id,
                            &res.task_id,
                            &(Box::new(io::Error::new(
                                io::ErrorKind::Interrupted,
                                format!("Max retries of {} exceeded", ctx.max_attempts),
                            )) as BoxDynError),
                        )
                        .await
                    }
                }
            },
        }
    }
}

impl<T, C> RedisStorage<T, C>
where
    T: DeserializeOwned + Send + Unpin + Send + Sync + 'static,
    C: Codec<Compact = Vec<u8>>,
{
    /// 从 Redis 队列中获取任务
    async fn fetch_next(
        &mut self,
        worker_id: &str,
    ) -> Result<Vec<TaskRequest<T, RedisContext>>, RedisError> {
        let fetch_jobs = self.scripts.get_jobs.clone();
        let consumers_set = self.config.consumers_set();
        let active_jobs_list = self.config.active_jobs_list();
        let job_data_hash = self.config.job_data_hash();
        let inflight_set = format!("{}:{}", self.config.inflight_jobs_set(), worker_id);
        let signal_list = self.config.signal_list();
        let namespace = &self.config.namespace;

        let result:Result<Vec<Value>, RedisError> = self
            .client
            .evalsha(
                &fetch_jobs,
                vec![
                    consumers_set,
                    active_jobs_list,
                    inflight_set,
                    job_data_hash,
                    signal_list,
                ],
                vec![self.config.buffer_size.to_string(), worker_id.to_string()],
            )
            .await;

        match result {
            Ok(jobs) => {
                let mut processed = vec![];
                for job in jobs {
                    let bytes = deserialize_job(&job)?;
                    let mut request: TaskRequest<T, RedisContext> =
                        C::decode(Vec::from(bytes.as_bytes())).map_err(|e| build_error(&e.into().to_string()))?;
                    request.parts.context.lock_by = Some(worker_id.to_string());
                    // request.parts.namespace = Some(Namespace(namespace.clone()));
                    processed.push(request)
                }
                Ok(processed)
            }
            Err(e) => {
                warn!("An error occurred during streaming jobs: {e}");
                if matches!(e.kind(), ErrorKind::Unknown)
                    && e.to_string().contains("consumer not registered script")
                {
                    self.keep_alive(worker_id).await?;
                }
                Err(e)
            }
        }
    }
}

/// 构建 Redis 错误
fn build_error(message: &str) -> RedisError {
    RedisError::from(io::Error::new(io::ErrorKind::InvalidData, message))
}

/// 从 Redis 中反序列化任务请求
fn deserialize_job(job: &Value) -> Result<&Str, RedisError> {
    match job {
        Value::String(bytes) => Ok(bytes),
        Value::Array(val)=> val
            .first()
            .and_then(|val| {
                if let Value::String(bytes) = val {
                    Some(bytes)
                } else {
                    None
                }
            })
            .ok_or(build_error("Value::Bulk: Invalid data returned by storage")),
        _ => Err(build_error("unknown result type for next message")),
    }
}

impl<T, C> RedisStorage<T, C> {
    /// 保持活动状态
    /// 工作者在执行任务时，会定期调用此方法来更新其最后活跃时间
    async fn keep_alive(&mut self, worker_id: &str) -> Result<(), RedisError> {
        let register_consumer = self.scripts.register_consumer.clone();
        let inflight_set = format!("{}:{}", self.config.inflight_jobs_set(), worker_id);
        let consumers_set = self.config.consumers_set();

        let now: i64 = Utc::now().timestamp();
        info!("keep alive : {worker_id}");
        let _: () = self.client
            .evalsha(&register_consumer, vec![consumers_set], vec![
                now.to_string(),
                inflight_set,
            ])
            .await?;
        Ok(())
    }
}

impl<T, C> Storage for RedisStorage<T, C>
where
    T: Serialize + DeserializeOwned + Send + 'static + Unpin + Sync,
    C: Codec<Compact = Vec<u8>> + Send + 'static,
{
    type Job = T;
    type Error = RedisError;
    type Context = RedisContext;

    type Compact = Vec<u8>;

    /// 此方法会将任务请求推送到 Redis 队列中
    async fn push_request(
        &mut self,
        req: TaskRequest<T, RedisContext>,
    ) -> Result<Parts<Self::Context>, RedisError> {
        let push_job = self.scripts.push_job.clone();
        let job_data_hash = self.config.job_data_hash();
        let active_jobs_list = self.config.active_jobs_list();
        let signal_list = self.config.signal_list();

        //序列化任务
        let job =
            C::encode(&req).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
        //添加任务
        let _: usize = self
            .client
            .evalsha(
                &push_job,
                vec![job_data_hash, active_jobs_list, signal_list],
                vec![req.parts.task_id.to_string(), String::from_utf8(job)?],
            )
            .await?;
        Ok(req.parts)
    }

    async fn push_raw_request(
        &mut self,
        req: TaskRequest<Self::Compact, Self::Context>,
    ) -> Result<Parts<Self::Context>, Self::Error> {
        let push_job = self.scripts.push_job.clone();
        let job_data_hash = self.config.job_data_hash();
        let active_jobs_list = self.config.active_jobs_list();
        let signal_list = self.config.signal_list();

        let job =
            C::encode(&req).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
       let _: usize = self.client
            .evalsha(
                &push_job,
                &[job_data_hash, active_jobs_list, signal_list],
                vec![req.parts.task_id.to_string(), String::from_utf8(job)?],
            )
            .await?;
        Ok(req.parts)
    }

    async fn schedule_request(
        &mut self,
        req: TaskRequest<Self::Job, RedisContext>,
        on: i64,
    ) -> Result<Parts<Self::Context>, RedisError> {
        let schedule_job = self.scripts.schedule_job.clone();
        let job_data_hash = self.config.job_data_hash();
        let scheduled_jobs_set = self.config.scheduled_jobs_set();
        let job =
            C::encode(&req).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;

       let _: usize = self.client
            .evalsha(
                &schedule_job,
                vec![job_data_hash, scheduled_jobs_set],
                vec![
                    req.parts.task_id.to_string(),
                    String::from_utf8(job)?,
                    on.to_string(),
                ],
            )
            .await?;
        Ok(req.parts)
    }

    ///正在运行的任务数量
    async fn len(&mut self) -> Result<i64, RedisError> {
        let pending_jobs: i64 = self.client.llen(self.config.active_jobs_list()).await?;
        Ok(pending_jobs)
    }

    /// 根据任务ID获取任务
    async fn fetch_by_id(
        &mut self,
        job_id: &TaskId,
    ) -> Result<Option<TaskRequest<Self::Job, RedisContext>>, RedisError> {
        let data: Option<Vec<u8>> = self
            .client
            .hget(self.config.job_data_hash(), job_id.to_string())
            .await?;
        // 反序列化任务
        let inner: TaskRequest<T, RedisContext> = C::decode(data.expect("must be a valid task"))
            .map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
        Ok(Some(inner))
    }

    /// 更新任务
    async fn update(&mut self, job: TaskRequest<T, RedisContext>) -> Result<(), RedisError> {
        let task_id = job.parts.task_id.to_string();
        let bytes =
            C::encode(&job).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
       let _:i64 = self.client
            .hset(self.config.job_data_hash(), (task_id, bytes))
            .await?;
        Ok(())
    }

    async fn reschedule(
        &mut self,
        job: TaskRequest<T, RedisContext>,
        wait: Duration,
    ) -> Result<(), RedisError> {
        let schedule_job = self.scripts.schedule_job.clone();
        let job_id = &job.parts.task_id;
        let worker_id = &job.parts.context.lock_by.clone().unwrap();
        let job =
            C::encode(&job).map_err(|e| RedisError::new(ErrorKind::IO, e.into().to_string()))?;
        let job_data_hash = self.config.job_data_hash();
        let scheduled_jobs_set = self.config.scheduled_jobs_set();
        let on: i64 = Utc::now().timestamp();
        let wait: i64 = wait
            .as_secs()
            .try_into()
            .map_err(|e: TryFromIntError| RedisError::new(ErrorKind::IO, e.to_string()))?;
        let inflight_set = format!("{}:{}", self.config.inflight_jobs_set(), worker_id);
        let failed_jobs_set = self.config.failed_jobs_set();
        let _: i64 = self.client.srem(inflight_set, job_id.to_string()).await?;

        //放入失败任务集合
        let _: () = self.client
            .zadd(
                failed_jobs_set,
                None,
                None,
                false,
                false,
                (on as f64, job_id.to_string()),
            )
            .await?;
       let _: usize = self.client
            .evalsha(
                &schedule_job,
                vec![job_data_hash, scheduled_jobs_set],
                vec![
                    job_id.to_string(),
                    String::from_utf8(job)?,
                    (on + wait).to_string(),
                ],
            )
            .await?;
        Ok(())
    }
    async fn is_empty(&mut self) -> Result<bool, RedisError> {
        self.len().map_ok(|res| res == 0).await
    }

    async fn vacuum(&mut self) -> Result<usize, RedisError> {
        let vacuum_script = self.scripts.vacuum.clone();
        // let res: usize = self
        //     .client
        //     .evalsha(
        //         &vacuum_script,
        //         vec![self.config.done_jobs_set(), self.config.job_data_hash()],
        //         vec![],
        //     )
        //     .await?;
        Ok(1)
    }
}

impl<T, C> RedisStorage<T, C>
where
    C: Codec<Compact = Vec<u8>> + Send + 'static,
{
    /// Attempt to retry a job
    pub async fn retry(&mut self, worker_id: &str, task_id: &TaskId) -> Result<i32, RedisError>
    where
        T: Send + DeserializeOwned + Serialize + Unpin + Sync + 'static,
    {
        // let retry_job = self.scripts.retry_job.clone();
        // let inflight_set = format!("{}:{}", self.config.inflight_jobs_set(), worker_id);
        // let scheduled_jobs_set = self.config.scheduled_jobs_set();
        // let job_data_hash = self.config.job_data_hash();
        // let job_fut = self.fetch_by_id(task_id);
        // let now: i64 = Utc::now().timestamp();
        // let res = job_fut.await?;
        // let conn = &mut self.conn;
        todo!()
        // match res {
        //     Some(job) => {
        //         let attempt = &job.parts.attempt;
        //         let max_attempts = &job.parts.context.max_attempts;
        //         if &attempt.current() >= &max_attempts {
        //             self.kill(
        //                 worker_id,
        //                 task_id,
        //                 &(Box::new(io::Error::new(
        //                     io::ErrorKind::Interrupted,
        //                     format!("Max retries of {} exceeded", max_attempts),
        //                 )) as BoxDynError),
        //             )
        //                 .await?;
        //             return Ok(1);
        //         }
        //         let job = C::encode(job)
        //             .map_err(|e| (ErrorKind::IoError, "Encode error", e.into().to_string()))?;
        //
        //         let res: Result<i32, RedisError> = retry_job
        //             .key(inflight_set)
        //             .key(scheduled_jobs_set)
        //             .key(job_data_hash)
        //             .arg(task_id.to_string())
        //             .arg(now)
        //             .arg(job)
        //             .invoke_async(conn)
        //             .await;
        //         match res {
        //             Ok(count) => Ok(count),
        //             Err(e) => Err(e),
        //         }
        //     }
        //     None => Err(RedisError::from((ErrorKind::ResponseError, "Id not found"))),
        // }
    }

    /// Attempt to kill a job
    pub async fn kill(
        &mut self,
        worker_id: &str,
        task_id: &TaskId,
        error: &BoxDynError,
    ) -> Result<(), RedisError> {
        let kill_job = self.scripts.kill_job.clone();
        let current_worker_id = format!("{}:{}", self.config.inflight_jobs_set(), worker_id);
        let job_data_hash = self.config.job_data_hash();
        let dead_jobs_set = self.config.dead_jobs_set();
        let now: i64 = Utc::now().timestamp();
        self.client
            .evalsha(
                &kill_job,
                vec![current_worker_id, dead_jobs_set, job_data_hash],
                vec![task_id.to_string(), now.to_string(), error.to_string()],
            )
            .await
    }

    /// Required to add scheduled jobs to the active set
    pub async fn enqueue_scheduled(&mut self, count: usize) -> Result<usize, RedisError> {
        let enqueue_jobs = self.scripts.enqueue_scheduled.clone();
        let scheduled_jobs_set = self.config.scheduled_jobs_set();
        let active_jobs_list = self.config.active_jobs_list();
        let signal_list = self.config.signal_list();
        let now: i64 = Utc::now().timestamp();
        let res = self
            .client
            .evalsha(
                &enqueue_jobs,
                vec![scheduled_jobs_set, active_jobs_list, signal_list],
                vec![now.to_string(), count.to_string()],
            )
            .await;
        match res {
            Ok(count) => Ok(count),
            Err(e) => Err(e),
        }
    }

    /// Re-enqueue some jobs that might be abandoned.
    pub async fn re_enqueue_active(&mut self, job_ids: Vec<&TaskId>) -> Result<(), RedisError> {
        let re_enqueue_active = self.scripts.re_enqueue_active.clone();
        let inflight_set: String = self.config.inflight_jobs_set().to_string();
        let active_jobs_list = self.config.active_jobs_list();
        let signal_list = self.config.signal_list();

        self
            .client
            .evalsha(
                &re_enqueue_active,
                vec![inflight_set, active_jobs_list, signal_list],
                vec![
                    job_ids
                        .into_iter()
                        .map(|j| j.to_string())
                        .collect::<Vec<String>>(),
                ],
            )
            .await
    }

    /// 由于工作者崩溃或长时间无响应而产生的孤儿任务重新放入到队列
    pub async fn re_enqueue_orphaned(
        &mut self,
        count: i32,
        dead_since: DateTime<Utc>,
    ) -> Result<usize, RedisError> {
        let re_enqueue_orphaned = self.scripts.re_enqueue_orphaned.clone();
        let consumers_set = self.config.consumers_set();
        let active_jobs_list = self.config.active_jobs_list();
        let signal_list = self.config.signal_list();
        //当前时间
        let dead_since = dead_since.timestamp();

        let res = self
            .client
            .evalsha(
                &re_enqueue_orphaned,
                vec![consumers_set, active_jobs_list],
                vec![dead_since, count as i64],
            )
            .await;
        match res {
            Ok(count) => Ok(count),
            Err(e) => Err(e),
        }
    }
}
