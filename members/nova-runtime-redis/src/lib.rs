mod expose;
mod redis_task_source;

use anyhow::Result;
use fred::prelude::Client;
use fred::prelude::*;
use fred::types::scripts::Library;
use fred::util as fred_utils;
use redis::RedisError;
pub use redis::aio::ConnectionManager;
pub use redis_task_source::Config;
pub use redis_task_source::RedisContext;
pub use redis_task_source::RedisPollError;
pub use redis_task_source::RedisQueueInfo;
pub use redis_task_source::RedisScript;
pub use redis_task_source::RedisStorage;
use tracing::error;

/// 创建redis脚本
/// 创建 Redis 脚本库
///
/// 从 Lua 脚本文件创建 Redis 脚本库，用于任务队列的各种操作。
/// 所有脚本都会预编译并缓存在 Redis 服务器中，提高执行效率。
///
/// # 参数
/// - `client`: Redis 客户端引用
///
/// # 返回值
/// 返回包含所有预编译脚本的 `RedisScript` 结构体
///
/// # 错误
/// 如果任何脚本编译失败，将返回 Redis 错误
pub async fn create_redis_script(client: &Client) -> Result<RedisScript> {
    // 并行加载所有 Lua 脚本以提高性能
    let done_job = include_str!("../lua/done_job.lua");
    let done_job_hash = fred_utils::sha1_hash(done_job);
    if !client.script_exists::<bool, _>(&done_job_hash).await? {
        let _: () = client.script_load(done_job).await?;
    }
    let push_job = include_str!("../lua/push_job.lua");
    let push_job_hash = fred_utils::sha1_hash(push_job);
    if !client.script_exists::<bool, _>(&push_job_hash).await? {
        let _: () = client.script_load(push_job).await?;
    }
    let retry_job = include_str!("../lua/retry_job.lua");
    let retry_job_hash = fred_utils::sha1_hash(retry_job);
    if !client.script_exists::<bool, _>(&retry_job_hash).await? {
        let _: () = client.script_load(retry_job).await?;
    }
    let enqueue_scheduled = include_str!("../lua/enqueue_scheduled_jobs.lua");
    let enqueue_scheduled_hash = fred_utils::sha1_hash(enqueue_scheduled);
    if !client.script_exists::<bool, _>(&enqueue_scheduled_hash).await? {
        let _: () = client.script_load(enqueue_scheduled).await?;
    }
    let get_jobs = include_str!("../lua/get_jobs.lua");
    let get_jobs_hash = fred_utils::sha1_hash(get_jobs);
    if !client.script_exists::<bool, _>(&get_jobs_hash).await? {
        let _: () = client.script_load(get_jobs).await?;
    }
    let register_consumer = include_str!("../lua/register_consumer.lua");
    let register_consumer_hash = fred_utils::sha1_hash(register_consumer);
    if !client.script_exists::<bool, _>(&register_consumer_hash).await? {
        let _: () = client.script_load(register_consumer).await?;
    }
    let kill_job = include_str!("../lua/kill_job.lua");
    let kill_job_hash = fred_utils::sha1_hash(kill_job);
    if !client.script_exists::<bool, _>(&kill_job_hash).await? {
        let _: () = client.script_load(kill_job).await?;
    }
    let re_enqueue_active = include_str!("../lua/reenqueue_active_jobs.lua");
    let re_enqueue_active_hash = fred_utils::sha1_hash(re_enqueue_active);
    if !client.script_exists::<bool, _>(&re_enqueue_active_hash).await? {
        let _: () = client.script_load(re_enqueue_active).await?;
    }
    let re_enqueue_orphaned = include_str!("../lua/reenqueue_orphaned_jobs.lua");
    let re_enqueue_orphaned_hash = fred_utils::sha1_hash(re_enqueue_orphaned);
    if !client.script_exists::<bool, _>(&re_enqueue_orphaned_hash).await? {
        let _: () = client.script_load(re_enqueue_orphaned).await?;
    }
    let schedule_job = include_str!("../lua/schedule_job.lua");
    let schedule_job_hash = fred_utils::sha1_hash(schedule_job);
    if !client.script_exists::<bool, _>(&schedule_job_hash).await? {
        let _: () = client.script_load(schedule_job).await?;
    }
    let vacuum = include_str!("../lua/vacuum.lua");
    let vacuum_hash = fred_utils::sha1_hash(vacuum);
    if !client.script_exists::<bool, _>(&vacuum_hash).await? {
        let _: () = client.script_load(vacuum).await?;
    }
    let stats = include_str!("../lua/stats.lua");
    let stats_hash = fred_utils::sha1_hash(stats);
    if !client.script_exists::<bool, _>(&stats_hash).await? {
        let _: () = client.script_load(stats).await?;
    }
    Ok(RedisScript {
        done_job: done_job_hash,
        push_job: push_job_hash,
        retry_job: retry_job_hash,
        enqueue_scheduled: enqueue_scheduled_hash,
        get_jobs: get_jobs_hash,
        register_consumer: register_consumer_hash,
        kill_job: kill_job_hash,
        re_enqueue_active: re_enqueue_active_hash,
        re_enqueue_orphaned: re_enqueue_orphaned_hash,
        schedule_job: schedule_job_hash,
        vacuum: vacuum_hash,
        stats: stats_hash,
    })
}
