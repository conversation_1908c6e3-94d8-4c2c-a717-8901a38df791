mod expose;
mod redis_task_source;

use anyhow::Result;
use fred::prelude::Client;
use fred::prelude::*;
use fred::util as fred_utils;
pub use redis::aio::ConnectionManager;
pub use redis_task_source::Config;
pub use redis_task_source::RedisContext;
pub use redis_task_source::RedisPollError;
pub use redis_task_source::RedisQueueInfo;
pub use redis_task_source::RedisScript;
pub use redis_task_source::RedisStorage;
use tracing::debug;
use tracing::error;
use tracing::info;
use tracing::instrument;
use tracing::warn;

/// 创建redis脚本
///
/// 并行加载所有 Lua 脚本以提高性能，自动检查脚本是否已存在
#[instrument(skip(client), level = "debug")]
pub async fn create_redis_script(client: &Client) -> Result<RedisScript> {
    info!("开始初始化 Redis Lua 脚本");
    let start_time = std::time::Instant::now();

    /// 辅助函数：加载单个脚本
    async fn load_script(client: &Client, content: &str, script_name: &str) -> Result<String> {
        let hash = fred_utils::sha1_hash(content);
        debug!("处理脚本: {}, 哈希: {}", script_name, hash);

        // 检查脚本是否已存在，如果不存在则加载
        if !client.script_exists::<bool, _>(&hash).await? {
            let _: () = client.script_load(content).await?;
        }
        Ok(hash)
    }

    // 并行加载所有脚本
    debug!("开始并行加载 12 个 Lua 脚本");
    let script_load_result = futures::try_join!(
        load_script(client, include_str!("../lua/done_job.lua"), "done_job"),
        load_script(client, include_str!("../lua/push_job.lua"), "push_job"),
        load_script(client, include_str!("../lua/retry_job.lua"), "retry_job"),
        load_script(
            client,
            include_str!("../lua/enqueue_scheduled_jobs.lua"),
            "enqueue_scheduled"
        ),
        load_script(client, include_str!("../lua/get_jobs.lua"), "get_jobs"),
        load_script(
            client,
            include_str!("../lua/register_consumer.lua"),
            "register_consumer"
        ),
        load_script(client, include_str!("../lua/kill_job.lua"), "kill_job"),
        load_script(
            client,
            include_str!("../lua/re_enqueue_active_jobs.lua"),
            "re_enqueue_active"
        ),
        load_script(
            client,
            include_str!("../lua/re_enqueue_orphaned_jobs.lua"),
            "re_enqueue_orphaned"
        ),
        load_script(
            client,
            include_str!("../lua/schedule_job.lua"),
            "schedule_job"
        ),
        load_script(client, include_str!("../lua/vacuum.lua"), "vacuum"),
        load_script(client, include_str!("../lua/stats.lua"), "stats"),
    );

    let (
        done_job,
        push_job,
        retry_job,
        enqueue_scheduled,
        get_jobs,
        register_consumer,
        kill_job,
        re_enqueue_active,
        re_enqueue_orphaned,
        schedule_job,
        vacuum,
        stats,
    ) = match script_load_result {
        Ok(scripts) => {
            let elapsed = start_time.elapsed();
            info!("所有脚本加载完成，耗时: {:?}", elapsed);
            scripts
        }
        Err(e) => {
            let elapsed = start_time.elapsed();
            error!("脚本加载失败，耗时: {:?}，错误: {}", elapsed, e);
            return Err(e);
        }
    };

    debug!("构建 RedisScript 结构体");
    let redis_script = RedisScript {
        done_job,
        push_job,
        retry_job,
        enqueue_scheduled,
        get_jobs,
        register_consumer,
        kill_job,
        re_enqueue_active,
        re_enqueue_orphaned,
        schedule_job,
        vacuum,
        stats,
    };

    let total_elapsed = start_time.elapsed();
    info!(
        "Redis 脚本初始化完成！总耗时: {:?}, 已加载 12 个脚本",
        total_elapsed
    );

    // 记录所有脚本的哈希值用于调试
    info!(
        "脚本哈希摘要: done_job={}, push_job={}, retry_job={}, enqueue_scheduled={}, get_jobs={}, \
         register_consumer={}, kill_job={}, re_enqueue_active={}, re_enqueue_orphaned={}, \
         schedule_job={}, vacuum={}, stats={}",
        &redis_script.done_job[..8],
        &redis_script.push_job[..8],
        &redis_script.retry_job[..8],
        &redis_script.enqueue_scheduled[..8],
        &redis_script.get_jobs[..8],
        &redis_script.register_consumer[..8],
        &redis_script.kill_job[..8],
        &redis_script.re_enqueue_active[..8],
        &redis_script.re_enqueue_orphaned[..8],
        &redis_script.schedule_job[..8],
        &redis_script.vacuum[..8],
        &redis_script.stats[..8]
    );

    Ok(redis_script)
}
