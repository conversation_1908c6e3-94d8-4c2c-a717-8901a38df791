--[[
重新入队孤儿任务

这个脚本用于处理由于工作者崩溃或长时间无响应而产生的孤儿任务。
它会找到过期的消费者，将其执行中的任务重新放回队列供其他工作者处理。

参数说明：
KEYS[1]: 活跃消费者集合 (sorted set) - 存储所有活跃工作者及其最后活跃时间
KEYS[2]: 活跃任务列表 (list) - 存储等待处理的任务ID队列
KEYS[3]: 信号列表 (list) - 用于通知工作者有新任务可处理

ARGV[1]: 过期时间戳 - 在此时间之前最后活跃的消费者被认为已过期
ARGV[2]: 最大处理任务数 - 限制单次操作处理的任务数量，防止阻塞

返回值：
返回本次操作实际处理的孤儿任务数量
--]]

-- 查找过期的消费者（最后活跃时间在指定时间戳之前）
-- 使用 LIMIT 限制返回数量，避免一次处理过多过期消费者
local consumers = redis.call("zrangebyscore", KEYS[1], 0, ARGV[1], "LIMIT", 0, ARGV[2])

-- 启用命令复制，确保在主从复制环境中的一致性
redis.replicate_commands()

-- 初始化剩余可处理的任务数量限制
local limit = tonumber(ARGV[2])

-- 遍历每个过期的消费者
for _,consumer in ipairs(consumers) do
  -- 从消费者的执行中任务集合中随机弹出任务
  -- 使用 spop 确保任务不会被重复处理
  local jobs = redis.call("spop", consumer, limit)
  -- 注意：使用 # 操作符替代已废弃的 table.getn 函数
  local count = #jobs

  -- 如果有孤儿任务，将它们重新推入活跃任务列表
  if count > 0 then
    redis.call("rpush", KEYS[2], unpack(jobs))
  end

  -- 如果该消费者的所有任务都已处理完，从活跃消费者集合中移除它
  -- 这样可以清理已死亡的工作者记录
  if count < limit then
    redis.call("zrem", KEYS[1], consumer)
  end

  -- 更新剩余可处理的任务数量
  limit = limit - count

  -- 如果已达到处理限制，停止处理更多消费者
  if limit <= 0 then
    break
  end
end

-- 计算本次实际处理的任务数量
local processed = tonumber(ARGV[2]) - limit

-- 如果处理了任何孤儿任务，发送信号通知工作者
if processed > 0 then
  -- 通知所有工作者有新任务可处理
  redis.call("del", KEYS[3])
  redis.call("lpush", KEYS[3], 1)
end

-- 返回处理的任务数量
return processed
