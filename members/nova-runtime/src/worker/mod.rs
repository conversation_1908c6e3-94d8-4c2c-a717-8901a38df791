mod call_all;
mod context;
mod logic;
mod ready;
mod runnable;
mod trace;
mod builder;

use std::ops::Deref;
use std::ops::DerefMut;

pub use context::*;
pub use logic::*;
pub use crate::mq::poller::*;
pub use crate::mq::poller_state::*;
pub use ready::*;
pub use runnable::*;
pub use trace::*;
pub use builder::*;
use serde::Deserialize;
use serde::Serialize;
///任务处理器
/// 任务处理器用于处理任务的状态和事件。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct Worker<T> {
    pub name: String,
    pub state: T,
}

impl<T> Worker<T> {
    /// 创建一个新的任务处理器实例
    pub fn new(name: impl Into<String>, state: T) -> Self {
        Self { name: name.into(), state }
    }

    /// 获取任务处理器的状态
    pub fn inner(&self) -> &T {
        &self.state
    }

    ///获取任务处理器的名称
    pub fn name(&self) -> &str {
        &self.name
    }
}


///便捷解引用
impl<T> Deref for Worker<T> {
    type Target = T;
    fn deref(&self) -> &Self::Target {
        &self.state
    }
}


///便捷可变引用
impl<T> DerefMut for Worker<T> {
    /// 获取任务处理器的状态的可变引用
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.state
    }
}

