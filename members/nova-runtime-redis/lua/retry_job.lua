--[[
重试失败的任务

这个脚本用于将执行失败的任务重新调度到未来的某个时间点执行。
它支持指数退避、延迟重试等重试策略，提高任务执行的可靠性。

参数说明：
KEYS[1]: 当前消费者的执行中任务集合 (set) - 存储该工作者正在处理的任务
KEYS[2]: 定时任务集合 (sorted set) - 存储按执行时间排序的定时任务
KEYS[3]: 任务数据哈希表 (hash) - 存储任务ID到任务数据的映射

ARGV[1]: 任务ID - 要重试的任务标识
ARGV[2]: 重试时间戳 - 任务下次执行的时间点
ARGV[3]: 失败信息 - 本次执行失败的原因或错误信息

返回值：
1 - 任务成功调度重试
0 - 任务不在执行中状态（可能已完成或不存在）
--]]

-- 从当前消费者的执行中任务集合中移除该任务
-- 确保任务确实在该消费者的执行列表中
local removed = redis.call("srem", KEYS[1], ARGV[1])

-- 如果任务确实从执行中集合移除成功
if removed == 1 then
  -- 将任务添加到定时任务集合，使用重试时间作为分数
  -- 任务将在指定时间被重新调度执行
  redis.call("zadd", KEYS[2], ARGV[2], ARGV[1])

  -- 获取原始任务数据
  local job = redis.call('HGET', KEYS[3], ARGV[1])

  -- 重置任务数据，为重试做准备
  -- 这可能包括重置重试计数、更新任务状态等
  redis.call("hset", KEYS[3], ARGV[1], job)

  -- 保存本次执行的失败信息
  -- 用于故障分析和重试策略调整
  -- 注意：使用 hset 替代已废弃的 hmset 命令
  local ns = "::result"
  redis.call("hset", KEYS[3] .. ns, ARGV[1], ARGV[3] )

end

-- 返回操作结果
return removed
