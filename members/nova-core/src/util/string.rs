///字符串增强
pub trait StringExt {
    fn capitalize(&self) -> String;
    fn snake_case(&self) -> String;
    fn camel_case(&self) -> String;
    fn pascal_case(&self) -> String;
    fn kebab_case(&self) -> String {
        self.snake_case().replace('_', "-")
    }
}

impl StringExt for String {
    fn capitalize(&self) -> String {
        if let Some(first_char) = self.chars().next() {
            let capitalized = first_char
                .to_uppercase()
                .chain(self.chars().skip(1))
                .collect();
            capitalized
        } else {
            String::new()
        }
    }

    fn snake_case(&self) -> String {
        let mut snake_case = String::new();

        for (i, c) in self.chars().enumerate() {
            if c.is_ascii_uppercase() && i > 0 {
                snake_case.push('_');
                snake_case.push(c.to_ascii_lowercase());
            } else {
                snake_case.push(c.to_ascii_lowercase());
            }
        }

        snake_case
    }

    fn camel_case(&self) -> String {
        let mut camel_case = String::new();
        let mut capitalize_next = false;

        for c in self.chars() {
            if c.is_alphanumeric() {
                if capitalize_next {
                    camel_case.push(c.to_uppercase().next().unwrap());
                    capitalize_next = false;
                } else {
                    camel_case.push(c);
                }
            } else {
                capitalize_next = true;
            }
        }

        camel_case
    }

    fn pascal_case(&self) -> String {
        let mut pascal_case = String::new();
        let mut capitalize_next = true;

        for c in self.chars() {
            if c.is_alphanumeric() {
                if capitalize_next {
                    pascal_case.push(c.to_uppercase().next().unwrap());
                    capitalize_next = false;
                } else {
                    pascal_case.push(c);
                }
            } else {
                capitalize_next = true;
            }
        }

        pascal_case
    }
}

impl StringExt for &str {
    fn capitalize(&self) -> String {
        self.to_string().capitalize()
    }

    fn snake_case(&self) -> String {
        self.to_string().snake_case()
    }

    fn camel_case(&self) -> String {
        self.to_string().camel_case()
    }

    fn pascal_case(&self) -> String {
        self.to_string().pascal_case()
    }
}

