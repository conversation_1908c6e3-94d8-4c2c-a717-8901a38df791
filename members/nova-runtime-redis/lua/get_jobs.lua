--[[
工作者获取任务

这个脚本用于工作者从任务队列中获取待处理的任务。它确保任务的原子性分配，
防止多个工作者获取同一个任务，并维护任务的执行状态跟踪。

参数说明：
KEYS[1]: 活跃消费者集合 (sorted set) - 存储已注册的工作者信息 consumer
KEYS[2]: 活跃任务列表 (list) - 存储等待处理的任务ID队列 active
KEYS[3]: 当前消费者的执行中任务集合 (set) - 跟踪该工作者正在处理的任务 inflight
KEYS[4]: 任务数据哈希表 (hash) - 存储任务ID到任务数据的映射 data
KEYS[5]: 信号列表 (list) - 用于协调工作者之间的等待状态 signal

ARGV[1]: 最大获取任务数 - 限制单次获取的任务数量
ARGV[2]: 当前消费者标识 - 用于验证消费者注册状态

返回值：
返回获取到的任务数据数组，如果没有任务则返回空数组
--]]

-- 确保消费者已注册
-- 使用 zscore 检查消费者是否在活跃消费者集合中
local registered = redis.call("zscore", KEYS[1], ARGV[2])
if not registered then
  error("consumer not registered")
end

-- 从活跃任务列表中获取指定数量的任务ID
-- 使用 lrange 获取列表头部的任务，实现FIFO队列
local job_ids = redis.call("lrange", KEYS[2], 0, ARGV[1] - 1)
-- 注意：使用 # 操作符替代已废弃的 table.getn 函数
local count = #job_ids
local results = {}

-- 如果获取到任务
if count > 0 then
  -- 将这些任务添加到当前消费者的执行中任务集合
  -- 用于跟踪哪些任务正在被该工作者处理
  redis.call("sadd", KEYS[3], unpack(job_ids))

  -- 从活跃任务列表中移除已分配的任务
  -- 使用 ltrim 删除列表头部的已分配任务
  redis.call("ltrim", KEYS[2], count, -1)

  -- 获取任务的详细数据
  -- 使用 hmget 批量获取任务数据，提高效率
  results = redis.call("hmget", KEYS[4], unpack(job_ids))
end

-- 如果获取的任务数少于请求数，说明队列中任务不足
-- 删除信号，让其他工作者进入等待状态，避免无效轮询
if count < tonumber(ARGV[1]) then
  redis.call("del", KEYS[5])
end

-- 返回获取到的任务数据
return results
