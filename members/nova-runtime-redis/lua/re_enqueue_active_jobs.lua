--[[
重新入队活跃任务

这个脚本用于将指定的任务从执行中状态重新放回到活跃任务队列中。
这通常用于工作者崩溃恢复、任务重新分配等场景。

参数说明：
KEYS[1]: 当前消费者的执行中任务集合 (set) - 存储该工作者正在处理的任务
KEYS[2]: 活跃任务列表 (list) - 存储等待处理的任务ID队列
KEYS[3]: 信号列表 (list) - 用于通知工作者有新任务可处理

ARGV[]: 任务ID列表 - 要重新入队的任务标识数组

返回值：
true - 操作完成（无论是否有任务被重新入队）
--]]

-- 遍历所有要重新入队的任务ID
for _,job_id in ipairs(ARGV) do
  -- 从当前消费者的执行中任务集合中移除该任务
  -- 确保任务确实在该消费者的执行列表中
  local removed = redis.call("srem", KEYS[1], job_id)

  -- 如果任务确实从执行中集合移除成功
  if removed == 1 then
    -- 将任务重新推入活跃任务列表的尾部
    -- 任务将重新等待被其他工作者处理
    redis.call("rpush", KEYS[2], job_id)
  end
end

-- 发送信号通知所有工作者有新任务可处理
-- 这确保了重新入队的任务能够被及时处理
redis.call("del", KEYS[3])
redis.call("lpush", KEYS[3], 1)

-- 返回操作完成标志
return true
