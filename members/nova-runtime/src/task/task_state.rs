use std::fmt;
use std::str::FromStr;
use serde::Deserialize;
use serde::Serialize;
use crate::error::NovaRuntimeError;

/// Represents the state of a job/task
#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, <PERSON>h, <PERSON>ialEq, Eq)]
pub enum State {
    /// Job is pending
    #[serde(alias = "Latest")]
    Pending,
    /// Job is in the queue but not ready for execution
    Scheduled,
    /// Job is running
    Running,
    /// Job was done successfully
    Done,
    /// Job has failed. Check `last_error`
    Failed,
    /// <PERSON> has been killed
    Killed,
}

impl Default for State {
    fn default() -> Self {
        State::Pending
    }
}

impl FromStr for State {
    type Err = NovaRuntimeError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Pending" | "Latest" => Ok(State::Pending),
            "Running" => Ok(State::Running),
            "Done" => Ok(State::Done),
            "Failed" => Ok(State::Failed),
            "Killed" => Ok(State::Killed),
            "Scheduled" => Ok(State::Scheduled),
            _ => Err(NovaRuntimeError::MissingData("Invalid Job state".to_string())),
        }
    }
}

impl fmt::Display for State {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match &self {
            State::Pending => write!(f, "Pending"),
            State::Running => write!(f, "Running"),
            State::Done => write!(f, "Done"),
            State::Failed => write!(f, "Failed"),
            State::Killed => write!(f, "Killed"),
            State::Scheduled => write!(f, "Scheduled"),
        }
    }
}
