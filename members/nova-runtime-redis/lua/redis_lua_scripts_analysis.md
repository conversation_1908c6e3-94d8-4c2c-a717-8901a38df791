# Redis Lua 脚本分析文档

## 🎯 **概述**

`nova-runtime-redis` 中的 Lua 脚本实现了一个完整的分布式任务队列系统。这些脚本利用 Redis 的原子性操作和 Lua 脚本的事务特性，确保任务处理的一致性和可靠性。

## 📋 **脚本功能分类**

### 🚀 **任务提交类**

#### 1. `push_job.lua` - 推送新任务
- **功能**: 将新任务添加到队列中
- **特点**: 使用 `hsetnx` 防止重复任务
- **流程**: 存储任务数据 → 加入活跃队列 → 发送信号

#### 2. `schedule_job.lua` - 调度定时任务
- **功能**: 将任务调度到未来某个时间执行
- **特点**: 支持延迟执行和定时任务
- **流程**: 清理旧数据 → 存储任务 → 加入定时队列

### 📥 **任务获取类**

#### 3. `get_jobs.lua` - 工作者获取任务
- **功能**: 工作者从队列中获取待处理任务
- **特点**: 原子性分配，防止重复获取
- **流程**: 验证消费者 → 获取任务 → 标记执行中 → 返回数据

#### 4. `enqueue_scheduled_jobs.lua` - 定时任务入队
- **功能**: 将到期的定时任务移入活跃队列
- **特点**: 批量处理，限制单次数量
- **流程**: 查找到期任务 → 移入活跃队列 → 发送信号

### ✅ **任务完成类**

#### 5. `done_job.lua` - 标记任务完成
- **功能**: 将任务标记为成功完成
- **特点**: 保存执行结果，记录完成时间
- **流程**: 移除执行中状态 → 加入完成集合 → 保存结果

#### 6. `kill_job.lua` - 终止任务
- **功能**: 强制终止正在执行的任务
- **特点**: 用于处理无法恢复的错误
- **流程**: 移除执行中状态 → 加入死亡集合 → 保存错误信息

#### 7. `retry_job.lua` - 重试失败任务
- **功能**: 将失败任务重新调度执行
- **特点**: 支持延迟重试和指数退避
- **流程**: 移除执行中状态 → 重新调度 → 保存失败信息

### 🔧 **系统管理类**

#### 8. `register_consumer.lua` - 注册工作者
- **功能**: 注册工作者并更新心跳时间
- **特点**: 用于工作者生命周期管理
- **流程**: 更新活跃时间 → 维护工作者列表

#### 9. `reenqueue_active_jobs.lua` - 重新入队任务
- **功能**: 将指定任务重新放回队列
- **特点**: 用于任务重新分配
- **流程**: 移除执行中状态 → 重新入队 → 发送信号

#### 10. `reenqueue_orphaned_jobs.lua` - 处理孤儿任务
- **功能**: 处理崩溃工作者留下的孤儿任务
- **特点**: 自动故障恢复机制
- **流程**: 找到过期工作者 → 重新分配任务 → 清理死亡工作者

### 📊 **监控统计类**

#### 11. `stats.lua` - 获取统计信息
- **功能**: 收集各状态任务的数量统计
- **特点**: 提供系统健康监控数据
- **返回**: [待处理, 运行中, 死亡, 失败, 成功]

#### 12. `vacuum.lua` - 清理数据
- **功能**: 清理已完成任务的数据
- **特点**: 释放内存，维护系统性能
- **流程**: 删除任务数据 → 清空完成列表 → 返回清理数量

## 🏗️ **数据结构设计**

### Redis 键命名规范
```
{namespace}:jobs:data          # 任务数据哈希表
{namespace}:jobs:active        # 活跃任务列表
{namespace}:jobs:scheduled     # 定时任务集合
{namespace}:jobs:done          # 完成任务集合
{namespace}:jobs:dead          # 死亡任务集合
{namespace}:consumers:active   # 活跃消费者集合
{namespace}:consumer:{id}      # 消费者执行中任务集合
{namespace}:signal             # 信号列表
{namespace}:jobs:data::result  # 任务结果哈希表
```

### 数据类型说明
- **Hash**: 存储任务数据和结果
- **List**: 实现 FIFO 队列和信号机制
- **Sorted Set**: 按时间排序的任务集合
- **Set**: 存储执行中任务，支持快速查找

## 🔄 **任务生命周期**

```mermaid
graph TD
    A[新任务] --> B{立即执行?}
    B -->|是| C[push_job.lua]
    B -->|否| D[schedule_job.lua]
    
    C --> E[活跃队列]
    D --> F[定时队列]
    
    F --> G[enqueue_scheduled_jobs.lua]
    G --> E
    
    E --> H[get_jobs.lua]
    H --> I[执行中]
    
    I --> J{执行结果}
    J -->|成功| K[done_job.lua]
    J -->|失败| L[retry_job.lua]
    J -->|致命错误| M[kill_job.lua]
    
    L --> F
    K --> N[完成集合]
    M --> O[死亡集合]
```

## ⚡ **性能优化特点**

### 1. **原子性操作**
- 所有脚本都是原子执行，确保数据一致性
- 避免了多个 Redis 命令之间的竞态条件

### 2. **批量处理**
- `enqueue_scheduled_jobs.lua` 支持批量调度
- `reenqueue_orphaned_jobs.lua` 批量处理孤儿任务

### 3. **信号机制**
- 使用信号列表通知工作者有新任务
- 避免无效的轮询，提高响应速度

### 4. **内存管理**
- `vacuum.lua` 定期清理已完成任务数据
- 防止内存无限增长

## 🛡️ **可靠性保证**

### 1. **故障恢复**
- 孤儿任务自动重新分配
- 工作者崩溃检测和清理

### 2. **重试机制**
- 支持任务失败重试
- 可配置的重试策略

### 3. **状态跟踪**
- 完整的任务状态管理
- 详细的执行结果记录

## 🔍 **监控和调试**

### 统计指标
- 各状态任务数量
- 工作者活跃状态
- 系统吞吐量

### 故障排查
- 死亡任务分析
- 失败原因追踪
- 性能瓶颈识别

## 🎯 **最佳实践**

1. **合理设置批量大小**: 平衡性能和响应时间
2. **定期执行清理**: 使用 `vacuum.lua` 清理数据
3. **监控孤儿任务**: 及时处理崩溃工作者
4. **配置重试策略**: 根据业务需求调整重试参数
5. **心跳监控**: 定期检查工作者健康状态

这套 Lua 脚本构成了一个完整、可靠、高性能的分布式任务队列系统，为 `nova-runtime-redis` 提供了强大的任务处理能力。
