use std::marker::PhantomData;
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use thiserror::Error;
use tower::Service;
use crate::BoxDynError;

#[derive(<PERSON><PERSON><PERSON>, <PERSON>bug, <PERSON><PERSON>)]
pub enum WorkerError {
    /// An error occurred while processing a job.
    #[error("Failed to process job: {0}")]
    ProcessingError(String),
    /// An error occurred in the worker's service.
    #[error("Service error: {0}")]
    ServiceError(String),
    /// An error occurred while trying to start the worker.
    #[error("Failed to start worker: {0}")]
    StartError(String),
}

#[derive(<PERSON><PERSON><PERSON>, Debu<PERSON>, <PERSON><PERSON>)]
#[non_exhaustive]
pub enum NovaRuntimeError {
    /// An error occurred during execution.
    #[error("FailedError: {0}")]
    Failed(#[source] Arc<BoxDynError>),

    /// Execution was aborted
    #[error("AbortError: {0}")]
    Abort(#[source] Arc<BoxDynError>),

    #[doc(hidden)]
    /// Encountered an error during worker execution
    /// This should not be used inside a task function
    #[error("WorkerError: {0}")]
    WorkerError(WorkerError),

    /// Missing some data and yet it was requested during execution.
    /// This should not be used inside a task function
    #[error("MissingDataError: {0}")]
    MissingData(String),

    #[doc(hidden)]
    /// Encountered an error during service execution
    /// This should not be used inside a task function
    #[error("Encountered an error during service execution")]
    ServiceError(#[source] Arc<BoxDynError>),

    #[doc(hidden)]
    /// Encountered an error during service execution
    /// This should not be used inside a task function
    #[error("Encountered an error during streaming")]
    SourceError(#[source] Arc<BoxDynError>),
}


impl From<BoxDynError> for NovaRuntimeError {
    fn from(err: BoxDynError) -> Self {
        if let Some(e) = err.downcast_ref::<NovaRuntimeError>() {
            e.clone()
        } else {
            NovaRuntimeError::Failed(Arc::new(err))
        }
    }
}

#[derive(Clone, Debug)]
pub struct ErrorHandlingLayer {
    _p: PhantomData<()>,
}

impl ErrorHandlingLayer {
    /// Create a new ErrorHandlingLayer
    pub fn new() -> Self {
        Self { _p: PhantomData }
    }
}

impl Default for ErrorHandlingLayer {
    fn default() -> Self {
        Self::new()
    }
}

impl<S> tower::layer::Layer<S> for ErrorHandlingLayer {
    type Service = ErrorHandlingService<S>;

    fn layer(&self, service: S) -> Self::Service {
        ErrorHandlingService { service }
    }
}

/// The underlying service
#[derive(Clone, Debug)]
pub struct ErrorHandlingService<S> {
    service: S,
}

impl<S, Request> Service<Request> for ErrorHandlingService<S>
where
    S: Service<Request>,
    S::Error: Into<BoxDynError>,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = NovaRuntimeError;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.service.poll_ready(cx).map_err(|e| {
            let boxed_error: BoxDynError = e.into();
            boxed_error.into()
        })
    }

    fn call(&mut self, req: Request) -> Self::Future {
        let fut = self.service.call(req);

        Box::pin(async move {
            fut.await.map_err(|e| {
                let boxed_error: BoxDynError = e.into();
                boxed_error.into()
            })
        })
    }
}
