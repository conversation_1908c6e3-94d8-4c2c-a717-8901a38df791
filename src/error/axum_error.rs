use crate::NovaError;
use axum::{
    response::{IntoResponse, Response},
    Json,
};
use http::StatusCode;

///实现axum错误返回
impl IntoResponse for NovaError {
    fn into_response(self) -> Response {
        (&self).into_response()
    }
}

impl IntoResponse for &NovaError {
    fn into_response(self) -> Response {
        let body = self.to_owned().as_application().as_json();
        let status: StatusCode = self.into();
        (status, J<PERSON>(body)).into_response()
    }
}
