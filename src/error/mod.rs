pub mod axum_error;

use crate::prelude::StringExt;
use http::StatusCode;
use serde::Serialize;
use serde_json::{json, Value};
use std::convert::AsRef;
use std::{
    error::Error as StdError,
    fmt::{<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtR<PERSON>ult},
    sync::Arc,
};
use strum::AsRefStr;
use thiserror::Error as ThisError;
///定义错误类型
pub trait ErrorMeta {
    fn code(&self) -> ErrorCode;
    fn key(&self) -> ErrorKey;
    fn message(&self) -> ErrorMessage;
    fn meta(&self) -> Option<Box<Value>>;
}

#[derive(Debug, Clone, Hash, Eq, PartialEq, Ord, PartialOrd, Serialize)]
pub struct ErrorCode(u16);

impl ErrorCode {
    pub fn as_u16(&self) -> u16 {
        self.0
    }
}

impl Display for ErrorCode {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.0)
    }
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Hash, Eq, PartialEq, Ord, PartialOrd, Serialize)]
pub struct ErrorKey(String);

impl ErrorKey {
    pub fn internal(key: &str, subtype: Option<&str>) -> Self {
        if let Some(subtype) = subtype {
            ErrorKey(format!("err::internal::{}::{}", key, subtype))
        } else {
            ErrorKey(format!("err::internal::{}", key))
        }
    }

    pub fn application(key: &str, subtype: Option<&str>) -> Self {
        if let Some(subtype) = subtype {
            ErrorKey(format!("err::application::{}::{}", key, subtype))
        } else {
            ErrorKey(format!("err::application::{}", key))
        }
    }
}

impl Display for ErrorKey {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.0)
    }
}

#[derive(Debug, Clone, Hash, Eq, PartialEq, Ord, PartialOrd, Serialize)]
pub struct ErrorMessage(String);

impl AsRef<str> for ErrorMessage {
    fn as_ref(&self) -> &str {
        &self.0
    }
}

impl Display for ErrorMessage {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.0)
    }
}

#[derive(ThisError, Clone, Eq, PartialEq, Serialize, AsRefStr)]
#[serde(rename_all = "camelCase")]
#[strum(serialize_all = "PascalCase")]
pub enum InternalError {
    #[error("An unknown error occurred: {}", .message)]
    UnknownError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("A unique field violation occurred: {}", .message)]
    UniqueFieldViolation {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("A timeout occurred: {}", .message)]
    Timeout {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("A connection error occurred: {}", .message)]
    ConnectionError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Entity not found: {}", .message)]
    KeyNotFound {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Argument provided is invalid: {}", .message)]
    InvalidArgument {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("An error while performing an IO operation: {}", .message)]
    IOErr {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Encription error: {}", .message)]
    EncryptionError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Decryption error: {}", .message)]
    DecryptionError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Configuration error: {}", .message)]
    ConfigurationError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Serialization error: {}", .message)]
    SerializeError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Deserialization error: {}", .message)]
    DeserializeError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("An error occurred running the javascript function: {}", .message)]
    ScriptError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
}

impl From<anyhow::Error> for InternalError {
    fn from(error: anyhow::Error) -> Self {
        match error.downcast_ref::<InternalError>() {
            Some(integration_error) => integration_error.clone(),
            None => InternalError::UnknownError {
                message: error.to_string(),
                subtype: None,
                meta: None,
            },
        }
    }
}

impl InternalError {
    pub fn unknown(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::UnknownError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn unique_field_violation(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::UniqueFieldViolation {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn timeout(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::Timeout {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn script_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::ScriptError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn serialize_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::SerializeError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn deserialize_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::DeserializeError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn configuration_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::ConfigurationError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn encryption_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::EncryptionError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn decryption_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::DecryptionError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn connection_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::ConnectionError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn io_err(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::IOErr {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn key_not_found(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::KeyNotFound {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn invalid_argument(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::internal(InternalError::InvalidArgument {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    fn set_meta(self, metadata: Box<Value>) -> Self {
        match self {
            InternalError::UnknownError {
                message, subtype, ..
            } => InternalError::UnknownError {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::UniqueFieldViolation {
                message, subtype, ..
            } => InternalError::UniqueFieldViolation {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::Timeout {
                message, subtype, ..
            } => InternalError::Timeout {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::ConnectionError {
                message, subtype, ..
            } => InternalError::ConnectionError {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::KeyNotFound {
                message, subtype, ..
            } => InternalError::KeyNotFound {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::InvalidArgument {
                message, subtype, ..
            } => InternalError::InvalidArgument {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::IOErr {
                message, subtype, ..
            } => InternalError::IOErr {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::EncryptionError {
                message, subtype, ..
            } => InternalError::EncryptionError {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::DecryptionError {
                message, subtype, ..
            } => InternalError::DecryptionError {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::ConfigurationError {
                message, subtype, ..
            } => InternalError::ConfigurationError {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::ScriptError {
                message, subtype, ..
            } => InternalError::ScriptError {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::SerializeError {
                message, subtype, ..
            } => InternalError::SerializeError {
                message,
                subtype,
                meta: Some(metadata),
            },
            InternalError::DeserializeError {
                message, subtype, ..
            } => InternalError::DeserializeError {
                message,
                subtype,
                meta: Some(metadata),
            },
        }
    }
}

impl ErrorMeta for InternalError {
    fn code(&self) -> ErrorCode {
        match self {
            InternalError::UnknownError { .. } => ErrorCode(1000),
            InternalError::UniqueFieldViolation { .. } => ErrorCode(1001),
            InternalError::Timeout { .. } => ErrorCode(1002),
            InternalError::ConnectionError { .. } => ErrorCode(1003),
            InternalError::KeyNotFound { .. } => ErrorCode(1004),
            InternalError::InvalidArgument { .. } => ErrorCode(1005),
            InternalError::IOErr { .. } => ErrorCode(1006),
            InternalError::EncryptionError { .. } => ErrorCode(1007),
            InternalError::DecryptionError { .. } => ErrorCode(1008),
            InternalError::ConfigurationError { .. } => ErrorCode(1009),
            InternalError::ScriptError { .. } => ErrorCode(1010),
            InternalError::SerializeError { .. } => ErrorCode(1011),
            InternalError::DeserializeError { .. } => ErrorCode(1012),
        }
    }

    fn key(&self) -> ErrorKey {
        match self {
            InternalError::UnknownError { subtype, .. } => {
                ErrorKey::internal("unknown", subtype.as_deref())
            }
            InternalError::UniqueFieldViolation { subtype, .. } => {
                ErrorKey::internal("unique_violation", subtype.as_deref())
            }
            InternalError::Timeout { subtype, .. } => {
                ErrorKey::internal("timeout", subtype.as_deref())
            }
            InternalError::ConnectionError { subtype, .. } => {
                ErrorKey::internal("connection_error", subtype.as_deref())
            }
            InternalError::KeyNotFound { subtype, .. } => {
                ErrorKey::internal("key_not_found", subtype.as_deref())
            }
            InternalError::InvalidArgument { subtype, .. } => {
                ErrorKey::internal("invalid_argument", subtype.as_deref())
            }
            InternalError::IOErr { subtype, .. } => {
                ErrorKey::internal("io_err", subtype.as_deref())
            }
            InternalError::EncryptionError { subtype, .. } => {
                ErrorKey::internal("encryption_error", subtype.as_deref())
            }
            InternalError::DecryptionError { subtype, .. } => {
                ErrorKey::internal("decryption_error", subtype.as_deref())
            }
            InternalError::ConfigurationError { subtype, .. } => {
                ErrorKey::internal("configuration_error", subtype.as_deref())
            }
            InternalError::ScriptError { subtype, .. } => {
                ErrorKey::internal("script_error", subtype.as_deref())
            }
            InternalError::SerializeError { subtype, .. } => {
                ErrorKey::internal("serialize_error", subtype.as_deref())
            }
            InternalError::DeserializeError { subtype, .. } => {
                ErrorKey::internal("deserialize_error", subtype.as_deref())
            }
        }
    }

    fn message(&self) -> ErrorMessage {
        match self {
            InternalError::UnknownError { message, .. } => ErrorMessage(message.to_string()),
            InternalError::UniqueFieldViolation { message, .. } => {
                ErrorMessage(message.to_string())
            }
            InternalError::Timeout { message, .. } => ErrorMessage(message.to_string()),
            InternalError::ConnectionError { message, .. } => ErrorMessage(message.to_string()),
            InternalError::KeyNotFound { message, .. } => ErrorMessage(message.to_string()),
            InternalError::InvalidArgument { message, .. } => ErrorMessage(message.to_string()),
            InternalError::IOErr { message, .. } => ErrorMessage(message.to_string()),
            InternalError::EncryptionError { message, .. } => ErrorMessage(message.to_string()),
            InternalError::DecryptionError { message, .. } => ErrorMessage(message.to_string()),
            InternalError::ConfigurationError { message, .. } => ErrorMessage(message.to_string()),
            InternalError::ScriptError { message, .. } => ErrorMessage(message.to_string()),
            InternalError::SerializeError { message, .. } => ErrorMessage(message.to_string()),
            InternalError::DeserializeError { message, .. } => ErrorMessage(message.to_string()),
        }
    }

    fn meta(&self) -> Option<Box<Value>> {
        match self {
            InternalError::UnknownError { meta, .. } => meta.clone(),
            InternalError::UniqueFieldViolation { meta, .. } => meta.clone(),
            InternalError::Timeout { meta, .. } => meta.clone(),
            InternalError::ConnectionError { meta, .. } => meta.clone(),
            InternalError::KeyNotFound { meta, .. } => meta.clone(),
            InternalError::InvalidArgument { meta, .. } => meta.clone(),
            InternalError::IOErr { meta, .. } => meta.clone(),
            InternalError::EncryptionError { meta, .. } => meta.clone(),
            InternalError::DecryptionError { meta, .. } => meta.clone(),
            InternalError::ConfigurationError { meta, .. } => meta.clone(),
            InternalError::ScriptError { meta, .. } => meta.clone(),
            InternalError::SerializeError { meta, .. } => meta.clone(),
            InternalError::DeserializeError { meta, .. } => meta.clone(),
        }
    }
}

impl Debug for InternalError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        writeln!(f, "{}\n", &self)?;
        let mut current = self.source();

        while let Some(cause) = current {
            writeln!(f, "Caused by:\n\t{}", cause)?;
            current = cause.source();
        }

        Ok(())
    }
}

#[derive(ThisError, Clone, Eq, PartialEq, Serialize, AsRefStr)]
#[serde(rename_all = "camelCase")]
#[strum(serialize_all = "PascalCase")]
pub enum ApplicationError {
    #[error("Bad Request: {}", .message)]
    BadRequest {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Conflict: {}", .message)]
    Conflict {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Forbidden: {}", .message)]
    Forbidden {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Internal Server Error: {}", .message)]
    InternalServerError {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Method Not Allowed: {}", .message)]
    MethodNotAllowed {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Not Found: {}", .message)]
    NotFound {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Not Implemented: {}", .message)]
    NotImplemented {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Precondition Failed: {}", .message)]
    FailedDependency {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Service Unavailable: {}", .message)]
    ServiceUnavailable {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Too Many Requests: {}", .message)]
    TooManyRequests {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Unauthorized: {}", .message)]
    Unauthorized {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
    #[error("Unprocessable Entity: {}", .message)]
    UnprocessableEntity {
        message: String,
        subtype: Option<String>,
        meta: Option<Box<Value>>,
    },
}

impl From<anyhow::Error> for ApplicationError {
    fn from(error: anyhow::Error) -> Self {
        match error.downcast_ref::<ApplicationError>() {
            Some(integration_error) => integration_error.clone(),
            None => ApplicationError::InternalServerError {
                message: error.to_string(),
                subtype: None,
                meta: None,
            },
        }
    }
}

impl ApplicationError {
    pub fn bad_request(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::BadRequest {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn conflict(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::Conflict {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn forbidden(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::Forbidden {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn internal_server_error(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::InternalServerError {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn method_not_allowed(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::MethodNotAllowed {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn not_found(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::NotFound {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn not_implemented(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::NotImplemented {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn failed_dependency(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::FailedDependency {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn service_unavailable(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::ServiceUnavailable {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn too_many_requests(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::TooManyRequests {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn unauthorized(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::Unauthorized {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    pub fn unprocessable_entity(message: &str, subtype: Option<&str>) -> NovaError {
        NovaError::application(ApplicationError::UnprocessableEntity {
            message: message.to_string(),
            subtype: subtype.map(|s| s.to_string().snake_case()),
            meta: None,
        })
    }

    fn set_meta(self, meta: Box<Value>) -> Self {
        match self {
            ApplicationError::BadRequest {
                message, subtype, ..
            } => ApplicationError::BadRequest {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::Conflict {
                message, subtype, ..
            } => ApplicationError::Conflict {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::Forbidden {
                message, subtype, ..
            } => ApplicationError::Forbidden {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::InternalServerError {
                message, subtype, ..
            } => ApplicationError::InternalServerError {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::MethodNotAllowed {
                message, subtype, ..
            } => ApplicationError::MethodNotAllowed {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::NotFound {
                message, subtype, ..
            } => ApplicationError::NotFound {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::NotImplemented {
                message, subtype, ..
            } => ApplicationError::NotImplemented {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::FailedDependency {
                message, subtype, ..
            } => ApplicationError::FailedDependency {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::ServiceUnavailable {
                message, subtype, ..
            } => ApplicationError::ServiceUnavailable {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::TooManyRequests {
                message, subtype, ..
            } => ApplicationError::TooManyRequests {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::Unauthorized {
                message, subtype, ..
            } => ApplicationError::Unauthorized {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
            ApplicationError::UnprocessableEntity {
                message, subtype, ..
            } => ApplicationError::UnprocessableEntity {
                message: message.clone(),
                subtype: subtype.clone(),
                meta: Some(meta),
            },
        }
    }
}

impl ErrorMeta for ApplicationError {
    fn code(&self) -> ErrorCode {
        match self {
            ApplicationError::BadRequest { .. } => ErrorCode(2000),
            ApplicationError::Conflict { .. } => ErrorCode(2001),
            ApplicationError::Forbidden { .. } => ErrorCode(2002),
            ApplicationError::InternalServerError { .. } => ErrorCode(2003),
            ApplicationError::MethodNotAllowed { .. } => ErrorCode(2004),
            ApplicationError::NotFound { .. } => ErrorCode(2005),
            ApplicationError::NotImplemented { .. } => ErrorCode(2006),
            ApplicationError::FailedDependency { .. } => ErrorCode(2007),
            ApplicationError::ServiceUnavailable { .. } => ErrorCode(2008),
            ApplicationError::TooManyRequests { .. } => ErrorCode(2009),
            ApplicationError::Unauthorized { .. } => ErrorCode(2010),
            ApplicationError::UnprocessableEntity { .. } => ErrorCode(2011),
        }
    }

    fn key(&self) -> ErrorKey {
        match self {
            ApplicationError::BadRequest { subtype, .. } => {
                ErrorKey::application("bad_request", subtype.as_deref())
            }
            ApplicationError::Conflict { subtype, .. } => {
                ErrorKey::application("conflict", subtype.as_deref())
            }
            ApplicationError::Forbidden { subtype, .. } => {
                ErrorKey::application("forbidden", subtype.as_deref())
            }
            ApplicationError::InternalServerError { subtype, .. } => {
                ErrorKey::application("internal_server_error", subtype.as_deref())
            }
            ApplicationError::MethodNotAllowed { subtype, .. } => {
                ErrorKey::application("method_not_allowed", subtype.as_deref())
            }
            ApplicationError::NotFound { subtype, .. } => {
                ErrorKey::application("not_found", subtype.as_deref())
            }
            ApplicationError::NotImplemented { subtype, .. } => {
                ErrorKey::application("not_implemented", subtype.as_deref())
            }
            ApplicationError::FailedDependency { subtype, .. } => {
                ErrorKey::application("failed_dependency", subtype.as_deref())
            }
            ApplicationError::ServiceUnavailable { subtype, .. } => {
                ErrorKey::application("service_unavailable", subtype.as_deref())
            }
            ApplicationError::TooManyRequests { subtype, .. } => {
                ErrorKey::application("too_many_requests", subtype.as_deref())
            }
            ApplicationError::Unauthorized { subtype, .. } => {
                ErrorKey::application("unauthorized", subtype.as_deref())
            }
            ApplicationError::UnprocessableEntity { subtype, .. } => {
                ErrorKey::application("unprocessable_entity", subtype.as_deref())
            }
        }
    }

    fn message(&self) -> ErrorMessage {
        match self {
            ApplicationError::BadRequest { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::Conflict { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::Forbidden { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::InternalServerError { message, .. } => {
                ErrorMessage(message.to_string())
            }
            ApplicationError::MethodNotAllowed { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::NotFound { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::NotImplemented { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::FailedDependency { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::ServiceUnavailable { message, .. } => {
                ErrorMessage(message.to_string())
            }
            ApplicationError::TooManyRequests { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::Unauthorized { message, .. } => ErrorMessage(message.to_string()),
            ApplicationError::UnprocessableEntity { message, .. } => {
                ErrorMessage(message.to_string())
            }
        }
    }

    fn meta(&self) -> Option<Box<Value>> {
        match self {
            ApplicationError::BadRequest { meta, .. } => meta.clone(),
            ApplicationError::Conflict { meta, .. } => meta.clone(),
            ApplicationError::Forbidden { meta, .. } => meta.clone(),
            ApplicationError::InternalServerError { meta, .. } => meta.clone(),
            ApplicationError::MethodNotAllowed { meta, .. } => meta.clone(),
            ApplicationError::NotFound { meta, .. } => meta.clone(),
            ApplicationError::NotImplemented { meta, .. } => meta.clone(),
            ApplicationError::FailedDependency { meta, .. } => meta.clone(),
            ApplicationError::ServiceUnavailable { meta, .. } => meta.clone(),
            ApplicationError::TooManyRequests { meta, .. } => meta.clone(),
            ApplicationError::Unauthorized { meta, .. } => meta.clone(),
            ApplicationError::UnprocessableEntity { meta, .. } => meta.clone(),
        }
    }
}

impl Debug for ApplicationError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        writeln!(f, "{}\n", &self)?;
        let mut current = self.source();

        while let Some(cause) = current {
            writeln!(f, "Caused by:\n\t{}", cause)?;
            current = cause.source();
        }

        Ok(())
    }
}

impl From<InternalError> for ApplicationError {
    fn from(error: InternalError) -> Self {
        match error {
            InternalError::Timeout { .. }
            | InternalError::ConnectionError { .. }
            | InternalError::IOErr { .. }
            | InternalError::EncryptionError { .. }
            | InternalError::DecryptionError { .. }
            | InternalError::ScriptError { .. }
            | InternalError::ConfigurationError { .. }
            | InternalError::UnknownError { .. } => ApplicationError::InternalServerError {
                message: "An unknown error occurred".into(),
                subtype: None,
                meta: None,
            },
            InternalError::UniqueFieldViolation {
                message,
                subtype,
                meta,
            } => ApplicationError::Conflict {
                message,
                subtype,
                meta,
            },
            InternalError::KeyNotFound {
                message,
                subtype,
                meta,
            } => ApplicationError::NotFound {
                message,
                subtype,
                meta,
            },
            InternalError::InvalidArgument {
                message,
                subtype,
                meta,
            }
            | InternalError::SerializeError {
                message,
                subtype,
                meta,
            }
            | InternalError::DeserializeError {
                message,
                subtype,
                meta,
            } => ApplicationError::BadRequest {
                message,
                subtype,
                meta,
            },
        }
    }
}

#[derive(ThisError, Debug, Clone, Eq, PartialEq, Serialize)]
#[serde(untagged)]
pub enum NovaError {
    Internal(InternalError),
    Application(ApplicationError),
}

impl From<reqwest::Error> for NovaError {
    fn from(err: reqwest::Error) -> Self {
        InternalError::io_err(&err.to_string(), None)
    }
}

impl AsRef<str> for NovaError {
    fn as_ref(&self) -> &str {
        match self {
            NovaError::Internal(e) => e.as_ref(),
            NovaError::Application(e) => e.as_ref(),
        }
    }
}

impl From<anyhow::Error> for NovaError {
    fn from(error: anyhow::Error) -> Self {
        match error.downcast_ref::<NovaError>() {
            Some(integration_error) => match integration_error {
                internal @ NovaError::Internal(_) => internal.clone(),
                application @ NovaError::Application(_) => application.clone(),
            },
            None => NovaError::Internal(InternalError::UnknownError {
                message: error.to_string(),
                subtype: None,
                meta: None,
            }),
        }
    }
}

impl From<Arc<NovaError>> for NovaError {
    fn from(error: Arc<NovaError>) -> Self {
        Arc::unwrap_or_clone(error)
    }
}

impl<'a> From<&'a NovaError> for StatusCode {
    fn from(value: &'a NovaError) -> Self {
        match value {
            NovaError::Internal(e) => match e {
                InternalError::UniqueFieldViolation { .. } => StatusCode::CONFLICT,
                InternalError::Timeout { .. } => StatusCode::GATEWAY_TIMEOUT,
                InternalError::ConnectionError { .. } => StatusCode::BAD_GATEWAY,
                InternalError::KeyNotFound { .. } => StatusCode::NOT_FOUND,
                InternalError::InvalidArgument { .. }
                | InternalError::SerializeError { .. }
                | InternalError::DeserializeError { .. } => StatusCode::BAD_REQUEST,
                InternalError::UnknownError { .. }
                | InternalError::IOErr { .. }
                | InternalError::EncryptionError { .. }
                | InternalError::ConfigurationError { .. }
                | InternalError::ScriptError { .. }
                | InternalError::DecryptionError { .. } => StatusCode::INTERNAL_SERVER_ERROR,
            },
            NovaError::Application(e) => match e {
                ApplicationError::BadRequest { .. } => StatusCode::BAD_REQUEST,
                ApplicationError::Conflict { .. } => StatusCode::CONFLICT,
                ApplicationError::Forbidden { .. } => StatusCode::FORBIDDEN,
                ApplicationError::InternalServerError { .. } => StatusCode::INTERNAL_SERVER_ERROR,
                ApplicationError::MethodNotAllowed { .. } => StatusCode::METHOD_NOT_ALLOWED,
                ApplicationError::NotFound { .. } => StatusCode::NOT_FOUND,
                ApplicationError::NotImplemented { .. } => StatusCode::NOT_IMPLEMENTED,
                ApplicationError::FailedDependency { .. } => StatusCode::FAILED_DEPENDENCY,
                ApplicationError::ServiceUnavailable { .. } => StatusCode::SERVICE_UNAVAILABLE,
                ApplicationError::TooManyRequests { .. } => StatusCode::TOO_MANY_REQUESTS,
                ApplicationError::Unauthorized { .. } => StatusCode::UNAUTHORIZED,
                ApplicationError::UnprocessableEntity { .. } => StatusCode::UNPROCESSABLE_ENTITY,
            },
        }
    }
}

impl From<NovaError> for StatusCode {
    fn from(value: NovaError) -> Self {
        (&value).into()
    }
}

impl NovaError {
    pub fn status(&self) -> u16 {
        StatusCode::from(self).as_u16()
    }

    fn internal(internal: InternalError) -> Self {
        NovaError::Internal(internal)
    }

    fn application(application: ApplicationError) -> Self {
        NovaError::Application(application)
    }

    pub fn from_err_code(status: StatusCode, message: &str, subtype: Option<&str>) -> Self {
        let message = message.to_string();
        let subtype = subtype.map(|s| s.to_string());
        let meta = None;
        match status {
            StatusCode::BAD_REQUEST => NovaError::application(ApplicationError::BadRequest {
                message,
                subtype,
                meta,
            }),

            StatusCode::CONFLICT => NovaError::application(ApplicationError::Conflict {
                message,
                subtype,
                meta,
            }),

            StatusCode::FORBIDDEN => NovaError::application(ApplicationError::Forbidden {
                message,
                subtype,
                meta,
            }),

            StatusCode::INTERNAL_SERVER_ERROR => {
                NovaError::application(ApplicationError::InternalServerError {
                    message,
                    subtype,
                    meta,
                })
            }

            StatusCode::METHOD_NOT_ALLOWED => {
                NovaError::application(ApplicationError::MethodNotAllowed {
                    message,
                    subtype,
                    meta,
                })
            }

            StatusCode::NOT_FOUND => NovaError::application(ApplicationError::NotFound {
                message,
                subtype,
                meta,
            }),

            StatusCode::NOT_IMPLEMENTED => {
                NovaError::application(ApplicationError::NotImplemented {
                    message,
                    subtype,
                    meta,
                })
            }

            StatusCode::FAILED_DEPENDENCY => {
                NovaError::application(ApplicationError::FailedDependency {
                    message,
                    subtype,
                    meta,
                })
            }

            StatusCode::SERVICE_UNAVAILABLE => {
                NovaError::application(ApplicationError::ServiceUnavailable {
                    message,
                    subtype,
                    meta,
                })
            }

            StatusCode::TOO_MANY_REQUESTS => {
                NovaError::application(ApplicationError::TooManyRequests {
                    message,
                    subtype,
                    meta,
                })
            }

            StatusCode::UNAUTHORIZED => NovaError::application(ApplicationError::Unauthorized {
                message,
                subtype,
                meta,
            }),

            StatusCode::UNPROCESSABLE_ENTITY => {
                NovaError::application(ApplicationError::UnprocessableEntity {
                    message,
                    subtype,
                    meta,
                })
            }

            _ => {
                if status.is_client_error() {
                    NovaError::application(ApplicationError::BadRequest {
                        message,
                        subtype,
                        meta,
                    })
                } else {
                    NovaError::internal(InternalError::IOErr {
                        message: format!(
                            "Unknown error with status code: {}, message: {}",
                            status, message
                        ),
                        subtype,
                        meta,
                    })
                }
            }
        }
    }

    pub(crate) fn as_application(&self) -> NovaError {
        match self {
            NovaError::Application(e) => NovaError::Application(e.clone()),
            NovaError::Internal(e) => NovaError::Application(e.clone().into()),
        }
    }

    pub(crate) fn as_json(&self) -> serde_json::Value {
        json!({
            "type": self.as_ref(),
            "code": self.code().as_u16(),
            "status": StatusCode::from(self).as_u16(),
            "key": self.key().to_string(),
            "message": self.message().to_string(),
            "meta": self.meta().unwrap_or_default(),
        })
    }

    pub fn set_meta(self, meta: &Value) -> Self {
        match self {
            NovaError::Internal(e) => NovaError::internal(e.set_meta(Box::new(meta.clone()))),
            NovaError::Application(e) => NovaError::application(e.set_meta(Box::new(meta.clone()))),
        }
    }

    pub fn is_internal(&self) -> bool {
        matches!(self, NovaError::Internal(_))
    }

    pub fn is_application(&self) -> bool {
        matches!(self, NovaError::Application(_))
    }
}

impl ErrorMeta for NovaError {
    fn code(&self) -> ErrorCode {
        match self {
            NovaError::Internal(e) => e.code(),
            NovaError::Application(e) => e.code(),
        }
    }

    fn key(&self) -> ErrorKey {
        match self {
            NovaError::Internal(e) => e.key(),
            NovaError::Application(e) => e.key(),
        }
    }

    fn message(&self) -> ErrorMessage {
        match self {
            NovaError::Internal(e) => e.message(),
            NovaError::Application(e) => e.message(),
        }
    }

    fn meta(&self) -> Option<Box<Value>> {
        match self {
            NovaError::Internal(e) => e.meta(),
            NovaError::Application(e) => e.meta(),
        }
    }
}

impl Display for NovaError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        match self {
            NovaError::Internal(e) => write!(f, "{}", e),
            NovaError::Application(e) => write!(f, "{}", e),
        }
    }
}


