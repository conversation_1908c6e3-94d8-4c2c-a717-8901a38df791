--[[
将已到期的定时任务入队到活跃任务列表

这个脚本用于将已经到达执行时间的定时任务从定时任务集合中移动到活跃任务列表中，
使其可以被工作者立即处理。这是任务调度系统的核心组件之一。

参数说明：
KEYS[1]: 定时任务集合 (sorted set) - 存储按执行时间排序的定时任务
KEYS[2]: 活跃任务列表 (list) - 存储等待被工作者处理的任务
KEYS[3]: 信号列表 (list) - 用于通知工作者有新任务可处理

ARGV[1]: 当前时间戳 - 用于判断哪些任务已到执行时间
ARGV[2]: 最大调度任务数 - 限制单次操作处理的任务数量，防止阻塞

返回值：
返回本次操作实际调度的任务数量
--]]

-- 从定时任务集合中获取已到期的任务ID列表
-- 使用 zrangebyscore 获取分数在 0 到当前时间戳之间的任务
-- LIMIT 0, ARGV[2] 限制返回的任务数量，避免一次处理过多任务
local job_ids = redis.call("zrangebyscore", KEYS[1], 0, ARGV[1], "LIMIT", 0, ARGV[2])
-- 注意：使用 # 操作符替代已废弃的 table.getn 函数
local count = #job_ids

-- 如果有到期的任务需要处理
if count > 0 then
  -- 将这些任务ID推入活跃任务列表的尾部
  -- 使用 rpush 保证任务按照到期时间的顺序被处理
  redis.call("rpush", KEYS[2], unpack(job_ids))

  -- 从定时任务集合中移除已调度的任务
  -- 使用 zremrangebyrank 按排名删除，效率比逐个删除更高
  redis.call("zremrangebyrank", KEYS[1], 0, count - 1)

  -- 发送信号通知工作者有新任务可处理
  -- 先删除旧信号，再推入新信号，确保信号的时效性
  redis.call("del", KEYS[3])
  redis.call("lpush", KEYS[3], 1)
end

-- 返回本次调度的任务数量，用于监控和统计
return count
