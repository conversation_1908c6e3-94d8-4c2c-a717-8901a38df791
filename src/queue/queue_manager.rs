use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{Mutex, RwLock, Notify, Semaphore, broadcast};
use tokio::time::{sleep, timeout};
use tracing::{info, warn, error, debug};
use crate::queue::{Task, TaskStatus, TaskType};

#[derive(Clone)]
pub struct QueueManager {
    // 待处理任务队列
    pending_queue: Arc<Mutex<VecDeque<Task>>>,
    // 所有任务的存储 (task_id -> task)
    tasks: Arc<RwLock<HashMap<String, Task>>>,
    // 通知新任务到达
    notify: Arc<Notify>,
    // 队列统计信息
    stats: Arc<RwLock<QueueStats>>,
    // 同步请求并发限制
    sync_semaphore: Arc<Semaphore>,
    // 性能指标
    performance_metrics: Arc<RwLock<PerformanceMetrics>>,
    // 任务完成事件广播通道
    task_completion_tx: broadcast::Sender<TaskCompletionEvent>,
}

#[derive(Debug, <PERSON><PERSON>, Default, serde::Serialize)]
pub struct QueueStats {
    pub total_tasks: u64,
    pub pending_tasks: u64,
    pub running_tasks: u64,
    pub completed_tasks: u64,
    pub failed_tasks: u64,
    pub active_sync_requests: u64,
    pub avg_execution_time_ms: f64,
}

#[derive(Debug, Clone, Default)]
pub struct PerformanceMetrics {
    pub sync_request_count: u64,
    pub total_execution_time_ms: u64,
    pub notification_count: u64,
    pub polling_fallback_count: u64,
}

#[derive(Debug, Clone)]
pub struct TaskCompletionEvent {
    pub task_id: String,
    pub status: TaskStatus,
}

impl QueueManager {
    pub fn new() -> Self {
        Self::with_max_sync_concurrent(100) // 默认最大100个并发同步请求
    }

    pub fn with_max_sync_concurrent(max_concurrent: usize) -> Self {
        // 创建广播通道，容量设置为 1000，足够处理高并发场景
        let (task_completion_tx, _) = broadcast::channel(1000);

        Self {
            pending_queue: Arc::new(Mutex::new(VecDeque::new())),
            tasks: Arc::new(RwLock::new(HashMap::new())),
            notify: Arc::new(Notify::new()),
            stats: Arc::new(RwLock::new(QueueStats::default())),
            sync_semaphore: Arc::new(Semaphore::new(max_concurrent)),
            performance_metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            task_completion_tx,
        }
    }

    /// 提交新任务到队列
    pub async fn submit_task(&self, task: Task) -> String {
        let task_id = task.id.clone();
        
        info!("Submitting task {} of type {:?}", task_id, task.task_type);

        // 将任务添加到待处理队列
        {
            let mut queue = self.pending_queue.lock().await;
            queue.push_back(task.clone());
        }

        // 将任务添加到任务存储
        {
            let mut tasks = self.tasks.write().await;
            tasks.insert(task_id.clone(), task);
        }

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.total_tasks += 1;
            stats.pending_tasks += 1;
        }

        // 通知有新任务
        self.notify.notify_one();

        task_id
    }

    /// 获取下一个待处理任务
    pub async fn get_next_task(&self) -> Option<Task> {
        let mut queue = self.pending_queue.lock().await;
        queue.pop_front()
    }

    /// 等待新任务通知
    pub async fn wait_for_task(&self) {
        self.notify.notified().await;
    }

    /// 更新任务状态
    pub async fn update_task(&self, task_id: &str, task: Task) {
        let old_status = {
            let tasks = self.tasks.read().await;
            tasks.get(task_id).map(|t| t.status.clone())
        };

        let new_status = task.status.clone();

        {
            let mut tasks = self.tasks.write().await;
            tasks.insert(task_id.to_string(), task.clone());
        }

        // 更新统计信息
        if let Some(old_status) = old_status {
            let mut stats = self.stats.write().await;

            // 减少旧状态计数
            match old_status {
                TaskStatus::Pending => stats.pending_tasks = stats.pending_tasks.saturating_sub(1),
                TaskStatus::Running => stats.running_tasks = stats.running_tasks.saturating_sub(1),
                TaskStatus::Completed => stats.completed_tasks = stats.completed_tasks.saturating_sub(1),
                TaskStatus::Failed => stats.failed_tasks = stats.failed_tasks.saturating_sub(1),
                _ => {}
            }

            // 增加新状态计数
            match new_status {
                TaskStatus::Pending => stats.pending_tasks += 1,
                TaskStatus::Running => stats.running_tasks += 1,
                TaskStatus::Completed => stats.completed_tasks += 1,
                TaskStatus::Failed => stats.failed_tasks += 1,
                _ => {}
            }
        }

        // 🚀 发送任务完成通知（如果任务已完成、失败或取消）
        if matches!(new_status, TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Cancelled) {
            let event = TaskCompletionEvent {
                task_id: task_id.to_string(),
                status: new_status.clone(),
            };

            // 发送广播通知，忽略发送失败（没有接收者时）
            if let Err(_) = self.task_completion_tx.send(event) {
                debug!("No receivers for task completion event: {}", task_id);
            } else {
                debug!("Sent completion notification for task: {}", task_id);

                // 更新通知计数
                let mut metrics = self.performance_metrics.write().await;
                metrics.notification_count += 1;
            }
        }

        info!("Updated task {} status to {:?}", task_id, new_status);
    }

    /// 获取任务详情
    pub async fn get_task(&self, task_id: &str) -> Option<Task> {
        let tasks = self.tasks.read().await;
        tasks.get(task_id).cloned()
    }

    /// 获取所有任务
    pub async fn get_all_tasks(&self) -> Vec<Task> {
        let tasks = self.tasks.read().await;
        tasks.values().cloned().collect()
    }

    /// 获取队列统计信息
    pub async fn get_stats(&self) -> QueueStats {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// 取消任务
    pub async fn cancel_task(&self, task_id: &str) -> bool {
        let mut tasks = self.tasks.write().await;
        if let Some(task) = tasks.get_mut(task_id) {
            if task.status == TaskStatus::Pending || task.status == TaskStatus::Running {
                task.status = TaskStatus::Cancelled;
                info!("Cancelled task {}", task_id);
                return true;
            }
        }
        false
    }

    /// 重试失败的任务
    pub async fn retry_task(&self, task_id: &str) -> bool {
        let mut should_requeue = false;
        
        {
            let mut tasks = self.tasks.write().await;
            if let Some(task) = tasks.get_mut(task_id) {
                if task.can_retry() {
                    task.retry();
                    should_requeue = true;
                    info!("Retrying task {} (attempt {})", task_id, task.retry_count);
                }
            }
        }

        if should_requeue {
            if let Some(task) = self.get_task(task_id).await {
                let mut queue = self.pending_queue.lock().await;
                queue.push_back(task);
                self.notify.notify_one();
            }
        }

        should_requeue
    }

    /// 🚀 等待任务完成并返回结果（基于通知的优化版本）
    pub async fn wait_for_task_completion(&self, task_id: &str, timeout_seconds: u64) -> Option<Task> {
        // 获取同步请求许可证，限制并发数
        let _permit = match self.sync_semaphore.try_acquire() {
            Ok(permit) => permit,
            Err(_) => {
                warn!("Sync request limit reached, rejecting task {}", task_id);
                return None;
            }
        };

        // 更新活跃同步请求计数
        {
            let mut stats = self.stats.write().await;
            stats.active_sync_requests += 1;
        }

        let start_time = std::time::Instant::now();
        let timeout_duration = Duration::from_secs(timeout_seconds);

        // 首先检查任务是否已经完成
        if let Some(task) = self.get_task(task_id).await {
            if matches!(task.status, TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Cancelled) {
                debug!("Task {} already completed, returning immediately", task_id);
                self.update_sync_completion_stats(start_time).await;
                return Some(task);
            }
        } else {
            // 任务不存在
            self.update_sync_completion_stats(start_time).await;
            return None;
        }

        // 🚀 使用事件通知机制等待任务完成
        let result = self.wait_for_task_with_notification(task_id, timeout_duration).await;

        // 更新性能指标
        self.update_sync_completion_stats(start_time).await;

        match result {
            Some(task) => {
                info!("Task {} completed in {}ms via notification", task_id, start_time.elapsed().as_millis());
                Some(task)
            },
            None => {
                warn!("Task {} wait timeout after {} seconds", task_id, timeout_seconds);
                // 超时时仍然尝试获取任务状态
                self.get_task(task_id).await
            }
        }
    }

    /// 🚀 基于通知机制等待任务完成
    async fn wait_for_task_with_notification(&self, task_id: &str, timeout_duration: Duration) -> Option<Task> {
        // 订阅任务完成事件
        let mut rx = self.task_completion_tx.subscribe();

        // 使用 tokio::select! 同时等待通知和超时
        match timeout(timeout_duration, async {
            loop {
                // 等待任务完成通知
                match rx.recv().await {
                    Ok(event) => {
                        debug!("Received completion event for task: {}", event.task_id);

                        // 检查是否是我们等待的任务
                        if event.task_id == task_id {
                            debug!("Found target task completion: {}", task_id);
                            // 获取完整的任务信息
                            return self.get_task(task_id).await;
                        }
                        // 不是目标任务，继续等待
                    }
                    Err(broadcast::error::RecvError::Lagged(skipped)) => {
                        // 接收缓冲区满了，有消息被跳过
                        warn!("Task completion notification lagged, {} messages skipped", skipped);

                        // 降级到轮询模式检查任务状态
                        if let Some(task) = self.get_task(task_id).await {
                            if matches!(task.status, TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Cancelled) {
                                return Some(task);
                            }
                        }

                        // 更新降级计数
                        {
                            let mut metrics = self.performance_metrics.write().await;
                            metrics.polling_fallback_count += 1;
                        }

                        // 短暂等待后继续
                        sleep(Duration::from_millis(100)).await;
                    }
                    Err(broadcast::error::RecvError::Closed) => {
                        // 发送端关闭，不应该发生
                        error!("Task completion notification channel closed");
                        return None;
                    }
                }
            }
        }).await {
            Ok(result) => result,
            Err(_) => {
                debug!("Task {} notification wait timeout", task_id);
                None
            }
        }
    }

    /// 更新同步完成统计信息
    async fn update_sync_completion_stats(&self, start_time: std::time::Instant) {
        let execution_time_ms = start_time.elapsed().as_millis() as u64;

        {
            let mut metrics = self.performance_metrics.write().await;
            metrics.sync_request_count += 1;
            metrics.total_execution_time_ms += execution_time_ms;
        }

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.active_sync_requests = stats.active_sync_requests.saturating_sub(1);

            // 计算平均执行时间
            let metrics = self.performance_metrics.read().await;
            if metrics.sync_request_count > 0 {
                stats.avg_execution_time_ms = metrics.total_execution_time_ms as f64 / metrics.sync_request_count as f64;
            }
        }
    }

    /// 提交任务并等待完成
    pub async fn submit_and_wait(&self, task: Task, timeout_seconds: u64) -> Option<Task> {
        let task_id = self.submit_task(task).await;
        self.wait_for_task_completion(&task_id, timeout_seconds).await
    }

    /// 获取当前活跃的同步请求数量
    pub async fn get_active_sync_count(&self) -> u64 {
        let stats = self.stats.read().await;
        stats.active_sync_requests
    }

    /// 检查是否可以接受新的同步请求
    pub async fn can_accept_sync_request(&self) -> bool {
        self.sync_semaphore.available_permits() > 0
    }

    /// 获取性能指标
    pub async fn get_performance_metrics(&self) -> PerformanceMetrics {
        let metrics = self.performance_metrics.read().await;
        metrics.clone()
    }

    /// 重置性能指标（用于定期重置统计）
    pub async fn reset_performance_metrics(&self) {
        let mut metrics = self.performance_metrics.write().await;
        *metrics = PerformanceMetrics::default();

        let mut stats = self.stats.write().await;
        stats.avg_execution_time_ms = 0.0;
    }

    /// 清理已完成的任务 (可选的清理策略)
    pub async fn cleanup_completed_tasks(&self, max_age_seconds: u64) {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let mut tasks_to_remove = Vec::new();

        {
            let tasks = self.tasks.read().await;
            for (task_id, task) in tasks.iter() {
                if matches!(task.status, TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Cancelled) {
                    if let Some(completed_at) = task.completed_at {
                        if current_time - completed_at > max_age_seconds {
                            tasks_to_remove.push(task_id.clone());
                        }
                    }
                }
            }
        }

        if !tasks_to_remove.is_empty() {
            let mut tasks = self.tasks.write().await;
            for task_id in &tasks_to_remove {
                tasks.remove(task_id);
            }
            info!("Cleaned up {} completed tasks", tasks_to_remove.len());
        }
    }
}
