use crate::task::TaskRequest;
use crate::worker::context::Context;
use pin_project_lite::pin_project;
use std::pin::Pin;
use std::task::Poll;
use tower::{Layer, Service};

pin_project! {
    /// 异步任务跟踪器
    pub struct Tracked<F> {
       pub(crate) ctx: Context,
        #[pin]
       pub(crate) task: F,
    }
}


///实现Future trait
impl<F: Future> Future for Tracked<F> {
    type Output = F::Output;

    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {
        let this = self.project();

        match this.task.poll(cx) {
            res @ Poll::Ready(_) => {
                this.ctx.end_task();
                res
            }
            Poll::Pending => Poll::Pending,
        }
    }
}


///追踪层layer
#[derive(Debug, Clone)]
pub struct TrackerLayer {
    ctx: Context,
}

impl TrackerLayer {
  pub  fn new(ctx: Context) -> Self {
        Self { ctx }
    }
}

///tower layer实现
impl<S> Layer<S> for TrackerLayer {
    type Service = TrackerService<S>;

    fn layer(&self, service: S) -> Self::Service {
        //构建追踪服务
        TrackerService {
            ctx: self.ctx.clone(),
            service,
        }
    }
}

///追踪服务
#[derive(Debug, Clone)]
pub struct TrackerService<S> {
    ctx: Context,
    service: S,
}

///tower 追踪服务service实现
impl<S, Req, Ctx> Service<TaskRequest<Req, Ctx>> for TrackerService<S>
where
    S: Service<TaskRequest<Req, Ctx>>,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = Tracked<S::Future>;

    fn poll_ready(&mut self, cx: &mut std::task::Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.service.poll_ready(cx)
    }

    fn call(&mut self, request: TaskRequest<Req, Ctx>) -> Self::Future {
        // request.parts.attempt.increment();
        self.ctx.track(self.service.call(request))
    }
}