use std::any::Any;
use std::fmt;
use std::panic::{catch_unwind, AssertUnwindSafe};
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use tower::{Layer, Service};
use crate::error::NovaRuntimeError;
use crate::task::TaskRequest;

/// 可捕获panics的层.
/// 当服务层panic时,会调用`on_panic`函数.
#[derive(Clone, Debug)]
pub struct CatchPanicLayer<F> {
    on_panic: F,
}

impl CatchPanicLayer<fn(Box<dyn Any + Send>) -> NovaRuntimeError> {
    ///创建一个新的`CatchPanicLayer`实例.
    pub fn new() -> Self {
        CatchPanicLayer {
            on_panic: default_handler,
        }
    }
}

impl Default for CatchPanicLayer<fn(Box<dyn Any + Send>) -> NovaRuntimeError> {
    fn default() -> Self {
        Self::new()
    }
}

impl<F> CatchPanicLayer<F>
where
    F: FnMut(Box<dyn Any + Send>) -> NovaRuntimeError + Clone,
{
    /// Creates a new `CatchPanicLayer` with a custom panic handler.
    pub fn with_panic_handler(on_panic: F) -> Self {
        CatchPanicLayer { on_panic }
    }
}

impl<S, F> Layer<S> for CatchPanicLayer<F>
where
    F: FnMut(Box<dyn Any + Send>) -> NovaRuntimeError + Clone,
{
    type Service = CatchPanicService<S, F>;

    fn layer(&self, service: S) -> Self::Service {
        CatchPanicService {
            service,
            on_panic: self.on_panic.clone(),
        }
    }
}

/// 可捕获panics的服务.
/// 当服务层panic时,会调用`on_panic`函数.
/// 该函数用于处理panic,并返回一个错误.
#[derive(Clone, Debug)]
pub struct CatchPanicService<S, F> {
    service: S,
    on_panic: F,
}

impl<S, Req, Res, Ctx, F> Service<TaskRequest<Req, Ctx>> for CatchPanicService<S, F>
where
    S: Service<TaskRequest<Req, Ctx>, Response = Res, Error = NovaRuntimeError>,
    F: FnMut(Box<dyn Any + Send>) -> NovaRuntimeError + Clone,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = CatchPanicFuture<S::Future, F>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.service.poll_ready(cx)
    }

    fn call(&mut self, request: TaskRequest<Req, Ctx>) -> Self::Future {
        CatchPanicFuture {
            future: self.service.call(request),
            on_panic: self.on_panic.clone(),
        }
    }
}


pin_project_lite::pin_project! {
    /// 可捕获panics的future.
    /// 当future panic时,会调用`on_panic`函数.
    /// 该函数用于处理panic,并返回一个错误.
    pub struct CatchPanicFuture<Fut, F> {
        #[pin]
        future: Fut,
        on_panic: F,
    }
}

/// An error generated from a panic
#[derive(Debug, Clone)]
pub struct PanicError(pub String);

impl std::error::Error for PanicError {}

impl fmt::Display for PanicError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "PanicError: {}", self.0)
    }
}

impl<Fut, Res, F> Future for CatchPanicFuture<Fut, F>
where
    Fut: Future<Output = Result<Res, NovaRuntimeError>>,
    F: FnMut(Box<dyn Any + Send>) -> NovaRuntimeError,
{
    type Output = Result<Res, NovaRuntimeError>;

    ///轮询future,并捕获panic.
    /// 当future panic时,会调用`on_panic`函数.
    /// 该函数用于处理panic,并返回一个错误.
    fn poll(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Self::Output> {
        let this = self.as_mut().project();

        match catch_unwind(AssertUnwindSafe(|| this.future.poll(cx))) {
            Ok(res) => res,
            Err(e) => Poll::Ready(Err((this.on_panic)(e))),
        }
    }
}


/// 默认的panic处理函数
fn default_handler(e: Box<dyn Any + Send>) -> NovaRuntimeError {
    let panic_info = if let Some(s) = e.downcast_ref::<&str>() {
        s.to_string()
    } else if let Some(s) = e.downcast_ref::<String>() {
        s.clone()
    } else {
        "Unknown panic".to_string()
    };
    //抛出错误
    NovaRuntimeError::Abort(Arc::new(Box::new(PanicError(panic_info))))
}