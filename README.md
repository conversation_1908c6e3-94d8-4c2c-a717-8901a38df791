# Nova Lab - Axum 异步队列系统

这是一个基于 Axum 的异步队列系统实现，演示了如何将 HTTP 请求放入自定义队列中异步执行，并返回结果给用户。

## 🚀 特性

- ✅ **异步任务处理**: 请求立即返回任务ID，后台异步处理
- ✅ **多种任务类型**: 支持邮件发送、数据处理、文件上传、自定义任务
- ✅ **任务状态跟踪**: 实时查询任务执行状态和结果
- ✅ **重试机制**: 失败任务自动重试，可配置重试次数
- ✅ **任务管理**: 支持任务取消、手动重试
- ✅ **队列统计**: 实时查看队列状态和统计信息
- ✅ **多工作线程**: 并发处理多个任务
- ✅ **自动清理**: 定期清理已完成的旧任务
- ✅ **RESTful API**: 标准的 REST 接口
- ✅ **结构化日志**: 详细的执行日志

## 🏗️ 系统架构

```
HTTP Request → Axum Router → Queue Manager → Worker Pool → Task Processing
     ↓                                                           ↓
HTTP Response (Task ID)                               Update Task Status
     ↓                                                           ↓
Client Polling ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ←
```

### 核心组件

1. **Task (任务)**: 包含任务ID、类型、状态、载荷数据等
2. **QueueManager (队列管理器)**: 管理任务队列和状态
3. **Worker (工作线程)**: 异步处理任务
4. **Router (路由)**: 提供 RESTful API

## 📋 API 接口

### 异步提交任务
```bash
POST /api/v1/queue/tasks
```

### 🆕 同步提交任务（等待执行结果）
```bash
POST /api/v1/queue/tasks/sync
```

### 查询任务状态
```bash
GET /api/v1/queue/tasks/{task_id}
```

### 获取所有任务
```bash
GET /api/v1/queue/tasks
```

### 取消任务
```bash
POST /api/v1/queue/tasks/{task_id}/cancel
```

### 重试任务
```bash
POST /api/v1/queue/tasks/{task_id}/retry
```

### 获取队列统计
```bash
GET /api/v1/queue/stats
```

## 🔧 快速开始

### 1. 启动服务器
```bash
cargo run
```

### 2. 运行测试
```bash
./test_queue.sh
```

### 3. 手动测试

#### 异步提交任务：
```bash
curl -X POST http://localhost:8888/api/v1/queue/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "EmailSend",
    "payload": {
      "to": "<EMAIL>",
      "subject": "测试邮件",
      "body": "这是一个测试邮件"
    }
  }'
```

#### 🆕 同步提交任务（等待执行结果）：
```bash
curl -X POST http://localhost:8888/api/v1/queue/tasks/sync \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "DataProcess",
    "payload": {
      "data": [1, 2, 3, 4, 5],
      "operation": "sum"
    },
    "timeout_seconds": 30
  }'
```

查询任务状态：
```bash
curl http://localhost:8888/api/v1/queue/tasks/{task_id}
```

## 🔄 同步 vs 异步模式

### 异步模式 (`POST /api/v1/queue/tasks`)
- 立即返回任务ID
- 客户端需要轮询查询任务状态
- 适合长时间运行的任务
- 不会阻塞客户端

### 🆕 同步模式 (`POST /api/v1/queue/tasks/sync`)
- 等待任务完成后返回最终结果
- 包含执行时间统计
- 支持超时设置
- 适合需要立即获取结果的场景

#### 同步请求格式：
```json
{
  "task_type": "EmailSend",
  "payload": {
    "to": "<EMAIL>",
    "subject": "Hello",
    "body": "Email content"
  },
  "max_retries": 3,
  "timeout_seconds": 30
}
```

#### 同步响应格式：
```json
{
  "type": "success",
  "task_id": "e38eee64-264e-40da-a406-3642af0d3d20",
  "status": "Completed",
  "result": {
    "status": "sent",
    "to": "<EMAIL>",
    "subject": "Hello",
    "sent_at": "2025-09-06T00:01:54.380156+00:00"
  },
  "execution_time_ms": 2038,
  "message": "Task completed successfully"
}
```

## 📊 支持的任务类型

### 1. 邮件发送 (EmailSend)
```json
{
  "task_type": "EmailSend",
  "payload": {
    "to": "<EMAIL>",
    "subject": "Hello",
    "body": "Email content"
  }
}
```

### 2. 数据处理 (DataProcess)
```json
{
  "task_type": "DataProcess",
  "payload": {
    "data": [1, 2, 3, 4, 5],
    "operation": "sum"
  }
}
```

### 3. 文件上传 (FileUpload)
```json
{
  "task_type": "FileUpload",
  "payload": {
    "filename": "document.pdf",
    "file_size": 1024000
  }
}
```

### 4. 自定义任务 (Custom)
```json
{
  "task_type": {"Custom": "webhook"},
  "payload": {
    "url": "https://api.example.com/webhook"
  }
}
```

## 🛠️ 技术栈

- **Axum**: 现代异步 Web 框架
- **Tokio**: 异步运行时
- **Serde**: 序列化/反序列化
- **Tracing**: 结构化日志
- **UUID**: 唯一标识符生成
- **Chrono**: 时间处理

## 📁 项目结构

```
src/
├── queue/
│   ├── mod.rs              # 队列模块导出
│   ├── task.rs             # 任务定义
│   ├── queue_manager.rs    # 队列管理器
│   └── worker.rs           # 工作线程
├── router/
│   ├── mod.rs              # 路由模块
│   └── queue.rs            # 队列相关路由
├── config/                 # 配置模块
├── domain/                 # 领域模型
├── error/                  # 错误处理
├── server.rs               # 服务器实现
├── lib.rs                  # 库入口
└── main.rs                 # 程序入口
```

## 🔮 扩展建议

1. **持久化存储**: 使用 Redis 或数据库存储任务状态
2. **分布式队列**: 支持多实例部署
3. **优先级队列**: 支持任务优先级
4. **延迟任务**: 支持定时执行
5. **监控面板**: Web UI 查看队列状态
6. **任务依赖**: 支持任务间依赖关系
7. **批量操作**: 支持批量提交和查询任务
8. **消息队列集成**: 集成 RabbitMQ、Kafka 等
9. **任务限流**: 防止队列过载
10. **任务分片**: 支持大任务分片处理

## 📝 示例输出

测试脚本运行后的输出示例：

```json
{
  "type": "success",
  "task_id": "beff46c1-909e-4239-ad32-2fa3e0d924b9",
  "status": "Pending",
  "message": "Task submitted successfully"
}
```

任务完成后的状态：

```json
{
  "type": "success",
  "task": {
    "id": "beff46c1-909e-4239-ad32-2fa3e0d924b9",
    "task_type": "EmailSend",
    "status": "Completed",
    "result": {
      "status": "sent",
      "to": "<EMAIL>",
      "subject": "测试邮件",
      "sent_at": "2025-09-05T23:56:00.770006+00:00"
    }
  }
}
```

## 📄 许可证

MIT License
