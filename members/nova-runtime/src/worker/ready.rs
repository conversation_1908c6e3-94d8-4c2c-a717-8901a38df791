use std::fmt;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::task::Poll;
use futures::{Stream, StreamExt};
use futures::stream::BoxStream;
use futures::FutureExt;
use log::info;
use tower::{Layer, Service, ServiceBuilder};
use crate::BoxDynError;
use crate::coordinator::shutdown::Shutdown;
use crate::error::NovaRuntimeError;
use crate::task::{TaskRequest, TaskSource};
use crate::worker::call_all::CallAllUnordered;
use crate::worker::context::Context;
use crate::worker::logic::{Event, EventHandler};
use crate::worker::runnable::Runnable;
use crate::worker::trace::TrackerLayer;
use crate::worker::Worker;

/// 任务执行器运行任务状态
pub struct Ready<S, P> {
    service: S,
    source: P,
    pub(crate) shutdown: Option<Shutdown>,
    pub(crate) event_handler: EventHandler,
}


impl<S, P> fmt::Debug for Ready<S, P>
where
    S: fmt::Debug,
    P: fmt::Debug,
{
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Ready")
            .field("service", &self.service)
            .field("source", &self.source)
            .field("shutdown", &self.shutdown)
            .field("event_handler", &"...") // Avoid dumping potentially sensitive or verbose data
            .finish()
    }
}

impl<S, P> Clone for Ready<S, P>
where
    S: Clone,
    P: Clone,
{
    fn clone(&self) -> Self {
        Ready {
            service: self.service.clone(),
            source: self.source.clone(),
            shutdown: self.shutdown.clone(),
            event_handler: self.event_handler.clone(),
        }
    }
}

impl<S, P> Ready<S, P> {
    /// 构建一个运行状态的任务执行器
    pub fn new(service: S, poller: P) -> Self {
        Ready {
            service,
            source: poller,
            shutdown: None,
            event_handler: EventHandler::default(),
        }
    }
}



impl<S, P> Worker<Ready<S, P>> {
    /// 增加事件处理器
    pub fn on_event<F: Fn(Worker<Event>) + Send + Sync + 'static>(self, f: F) -> Self {
        let _ = self.event_handler.write().map(|mut res| {
            let _ = res.insert(Box::new(f));
        });
        self
    }

    /// 轮询任务流拉取任务
    fn poll_jobs<Svc, Stm, Req, Ctx>(
        worker: Worker<Context>,
        service: Svc,
        stream: Stm,
    ) -> BoxStream<'static, ()>
    where
        Svc: Service<TaskRequest<Req, Ctx>> + Send + 'static,
        Stm: Stream<Item = Result<Option<TaskRequest<Req, Ctx>>, NovaRuntimeError>> + Send + Unpin + 'static,
        Req: Send + 'static,
        Svc::Future: Send,
        Svc::Error: Send + 'static + Into<BoxDynError>,
        Ctx: Send + 'static,
    {
        let w = worker.clone();
        //消费消息
        let stream = stream.filter_map(move |result| {
            let worker = worker.clone();

            async move {
                match result {
                    Ok(Some(request)) => {
                        // 任务执行器开始执行任务消息
                        worker.emit(Event::Engage(request.parts.task_id.clone()));
                        Some(request)
                    }
                    Ok(None) => {
                        // 任务执行器空闲消息
                        worker.emit(Event::Idle);
                        None
                    }
                    Err(err) => {
                        // 任务执行器错误消息
                        worker.emit(Event::Error(Box::new(err)));
                        None
                    }
                }
            }
        });

        //包装任务流为无序调用所有任务的组合器
        let stream = CallAllUnordered::new(service, stream).map(move |res| {
            if let Err(error) = res {
                let error = error.into();
                if let Some(NovaRuntimeError::MissingData(_)) = error.downcast_ref::<NovaRuntimeError>() {
                    // 任务执行器停止消息
                    w.stop();
                }
                // 任务执行器错误消息
                w.emit(Event::Error(error));
            }
        });
        stream.boxed()
    }
    /// 运行任务执行器
    pub fn run<Req, Ctx>(self) -> Runnable
    where
        S: Service<TaskRequest<Req, Ctx>> + 'static,
        P: TaskSource<TaskRequest<Req, Ctx>> + 'static,
        Req: Send + 'static,
        S::Error: Send + 'static + Into<BoxDynError>,
        P::Stream: Unpin + Send + 'static,
        P::Layer: Layer<S>,
        <P::Layer as Layer<S>>::Service: Service<TaskRequest<Req, Ctx>> + Send,
        <<P::Layer as Layer<S>>::Service as Service<TaskRequest<Req, Ctx>>>::Future: Send,
        <<P::Layer as Layer<S>>::Service as Service<TaskRequest<Req, Ctx>>>::Error:
        Send + Into<BoxDynError>,
        Ctx: Send + 'static,
    {
        //获取service名字
        info!("构造任务执行器运行单元 {}", self.name);
        fn type_name_of_val<T>(_t: &T) -> &'static str {
            std::any::type_name::<T>()
        }
        let service = self.state.service;
        let worker_id = self.name;
        //构建任务执行器上下文
        let ctx = Context {
            running: Arc::default(),
            task_count: Arc::default(),
            waker: Arc::default(),
            shutdown: self.state.shutdown,
            event_handler: self.state.event_handler.clone(),
            is_ready: Arc::default(),
            service: type_name_of_val(&service).to_owned(),
        };
        let worker = Worker {
            name: worker_id.clone(),
            config: self.config.clone(),
            state: ctx.clone(),
        };
        let source = self.state.source;

        let poller = source.poll(&worker);
        let stream = poller.stream;
        let heartbeat = poller.heartbeat.boxed();
        let layer = poller.layer;
        //构建任务处理逻辑层
        let service = ServiceBuilder::new()
            .layer(TrackerLayer::new(worker.state.clone()))
            .layer(ReadinessLayer::new(worker.state.is_ready.clone()))
            // .layer(Data::new(worker.clone()))
            .layer(layer)
            .service(service);

        Runnable {
            poller: Self::poll_jobs(worker.clone(), service, stream),
            heartbeat,
            worker,
            running: false,
        }
    }
}


#[derive(Clone)]
struct ReadinessLayer {
    is_ready: Arc<AtomicBool>,
}

impl ReadinessLayer {
    fn new(is_ready: Arc<AtomicBool>) -> Self {
        Self { is_ready }
    }
}

impl<S> Layer<S> for ReadinessLayer {
    type Service = ReadinessService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        //返回准备就绪服务
        ReadinessService {
            inner,
            is_ready: self.is_ready.clone(),
        }
    }
}

/// 准备就绪服务
struct ReadinessService<S> {
    inner: S,
    is_ready: Arc<AtomicBool>,
}

impl<S, Request> Service<Request> for ReadinessService<S>
where
    S: Service<Request>,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = S::Future;

    ///检查服务是否准备就绪
    fn poll_ready(&mut self, cx: &mut std::task::Context<'_>) -> Poll<Result<(), Self::Error>> {
        // Delegate poll_ready to the inner service
        let result = self.inner.poll_ready(cx);
        //背压处理和资源管理机制
        match &result {
            Poll::Ready(Ok(_)) => self.is_ready.store(true, Ordering::Release),
            Poll::Pending | Poll::Ready(Err(_)) => self.is_ready.store(false, Ordering::Release),
        }

        result
    }

    ///处理请求
    fn call(&mut self, req: Request) -> Self::Future {
        self.inner.call(req)
    }
}
