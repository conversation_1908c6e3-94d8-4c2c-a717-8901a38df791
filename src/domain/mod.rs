use serde::{Deserialize, Serialize, Serializer};
use serde::ser::SerializeMap;
use serde_json::Value;

#[derive(Deserialize, Debug)]
pub struct ServerResponse<T>
where
    T: Serialize,
{
    pub response_type: String,
    pub args: T,
}

impl<T> Serialize for ServerResponse<T>
where
    T: Serialize,
{
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let args_value = match serde_json::to_value(&self.args) {
            Ok(value) => value,
            Err(_) => return Err(serde::ser::Error::custom("Serialization of args failed")),
        };

        if let Value::Object(ref args_map) = args_value {
            let mut state = serializer.serialize_map(None)?;

            state.serialize_entry("type", &self.response_type)?;

            for (key, value) in args_map {
                state.serialize_entry(key, value)?;
            }

            state.end()
        } else {
            self.args.serialize(serializer)
        }
    }
}

impl<T> ServerResponse<T>
where
    T: Serialize,
{
    pub fn new<R>(response_type: R, args: T) -> Self
    where
        R: Into<String>,
    {
        Self {
            response_type: response_type.into(),
            args,
        }
    }

    pub fn error(error: T) -> Self {
        Self {
            response_type: "error".to_string(),
            args: error,
        }
    }
}
