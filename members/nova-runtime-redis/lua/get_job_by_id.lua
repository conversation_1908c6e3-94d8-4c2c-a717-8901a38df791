--[[
工作者获取任务

这个脚本用于工作者从任务队列中获取待处理的任务。它确保任务的原子性分配，
防止多个工作者获取同一个任务，并维护任务的执行状态跟踪。

参数说明：
KEYS[1]: 任务数据哈希表 (hash) - 存储任务ID到任务数据的映射 data

ARGV[1]: 任务ID - 要获取的任务标识
ARGS[2]: 执行器ID - 任务关联的执行器

返回值：
返回获取到的任务数据数组，如果没有任务则返回空数组
--]]


-- 获取任务关联执行器
local job_worker_id = redis.call("get", ARGV[1])

local results = {}

-- 如果获取到关联执行器
if job_worker_id then
  -- 从活跃任务列表中移除已分配的任务
  -- 使用 ltrim 删除列表头部的已分配任务
  redis.call("ltrim", KEYS[1], count, -1)

  -- 获取任务的详细数据
  -- 使用 hmget 批量获取任务数据，提高效率
  results = redis.call("hmget", KEYS[2], unpack(job_ids))
end
-- 返回获取到的任务数据
return results
