use std::fmt;
use std::fmt::Debug;
use futures::future::BoxFuture;
use futures::FutureExt;
use tower::layer::util::Identity;

/// 任务轮询器
/// 任务轮询器用于从任务流中轮询任务，并将任务传递给任务处理函数
pub struct Poller<S, L = Identity> {
    /// 任务流
    /// 任务流用于从任务源中轮询任务
    pub stream: S,

    /// 心跳 Future
    /// 心跳 Future 用于定期执行心跳任务，例如检查任务源是否已连接
    pub heartbeat: BoxFuture<'static, ()>,

    /// 任务处理函数
    /// 任务处理函数用于处理从任务流中轮询到的任务
    pub layer: L,

    pub(crate) _priv: (),
}


impl<S> Poller<S, Identity> {
    /// 构造一个任务轮询器
    pub fn new(stream: S, heartbeat: impl Future<Output = ()> + Send + 'static) -> Self {
        Self::new_with_layer(stream, heartbeat, Identity::new())
    }

    /// 构造一个任务轮询器
    /// 任务轮询器用于从任务流中轮询任务，并将任务传递给任务处理函数
    pub fn new_with_layer<L>(
        stream: S,
        heartbeat: impl Future<Output = ()> + Send + 'static,
        layer: L,
    ) -> Poller<S, L> {
        Poller {
            stream,
            heartbeat: heartbeat.boxed(),
            layer,
            _priv: (),
        }
    }
}

impl<S, L> Debug for Poller<S, L>
where
    S: Debug,
    L: Debug,
{
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Poller")
            .field("stream", &self.stream)
            .field("heartbeat", &"...")
            .field("layer", &self.layer)
            .finish()
    }
}
