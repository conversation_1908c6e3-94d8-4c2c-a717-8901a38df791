use tower::layer::util::{Identity, Stack};
use crate::error::NovaRuntimeError;
use crate::layers::CatchPanicLayer;
use crate::worker::WorkerBuilder;

pub trait WorkerBuilderExt<Req, Ctx, Source, Middleware, Serv> {

    /// 添加一个新的layer `T` 到 [`WorkerBuilder`] 中.
    /// 如果 `layer` 为 `None`，则不添加任何layer.
    fn option_layer<T>(
        self,
        layer: Option<T>,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::Either<T, Identity>, Middleware>, Serv>;

    /// 添加一个 [`Layer`] 到 [`WorkerBuilder`] 中.
    /// 该layer会被添加到 [`WorkerBuilder`] 的middleware stack中.
    /// 如果 `layer` 为 `None`，则不添加任何layer.
    fn layer_fn<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::layer::LayerFn<F>, Middleware>, Serv>;


    /// 并发限制
    /// 限制同时处理的请求数量
    fn concurrency(
        self,
        max: usize,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::limit::ConcurrencyLimitLayer, Middleware>, Serv>;


    /// 限流
    /// 限制每秒处理的请求数量
    fn rate_limit(
        self,
        num: u64,
        per: std::time::Duration,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::limit::RateLimitLayer, Middleware>, Serv>;

    /// Retries failed requests according to the given retry policy.
    fn retry<P>(
        self,
        policy: P,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::retry::RetryLayer<P>, Middleware>, Serv>;

    /// Fails requests that take longer than `timeout`.
    fn timeout(
        self,
        timeout: std::time::Duration,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::timeout::TimeoutLayer, Middleware>, Serv>;

    /// Conditionally rejects requests based on `predicate`.
    fn filter<P>(
        self,
        predicate: P,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::filter::FilterLayer<P>, Middleware>, Serv>;

    /// Conditionally rejects requests based on an asynchronous `predicate`.
    fn filter_async<P>(
        self,
        predicate: P,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::filter::AsyncFilterLayer<P>, Middleware>, Serv>;

    /// Maps one request type to another.
    fn map_request<F, R1, R2>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::MapRequestLayer<F>, Middleware>, Serv>
    where
        F: FnMut(R1) -> R2 + Clone;

    /// Maps one response type to another.
    fn map_response<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::MapResponseLayer<F>, Middleware>, Serv>;

    /// Maps one error type to another.
    fn map_err<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::MapErrLayer<F>, Middleware>, Serv>;

    /// Composes a function that transforms futures produced by the service.
    fn map_future<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::MapFutureLayer<F>, Middleware>, Serv>;

    /// Applies an asynchronous function after the service, regardless of whether the future succeeds or fails.
    fn then<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::ThenLayer<F>, Middleware>, Serv>;

    /// Executes a new future after this service's future resolves.
    fn and_then<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::AndThenLayer<F>, Middleware>, Serv>;

    /// Maps the service's result type to a different value, regardless of success or failure.
    fn map_result<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, Source, Stack<tower::util::MapResultLayer<F>, Middleware>, Serv>;

    /// Catch panics in execution and pipe them as errors
    fn catch_panic(
        self,
    ) -> WorkerBuilder<
        Req,
        Ctx,
        Source,
        Stack<
            CatchPanicLayer<fn(Box<dyn std::any::Any + Send>) -> NovaRuntimeError>,
            Middleware,
        >,
        Serv,
    >;

    // fn enable_tracing(
    //     self,
    // ) -> WorkerBuilder<Req, Ctx, Source, Stack<tracing::TraceLayer, Middleware>, Serv>;
}

impl<Req, Ctx, Middleware, Serv> WorkerBuilderExt<Req, Ctx, (), Middleware, Serv>
for WorkerBuilder<Req, Ctx, (), Middleware, Serv>
{
    fn option_layer<T>(
        self,
        layer: Option<T>,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::Either<T, Identity>, Middleware>, Serv>
    {
        self.chain(|sb| sb.option_layer(layer))
    }

    fn layer_fn<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::layer::LayerFn<F>, Middleware>, Serv> {
        self.chain(|sb| sb.layer_fn(f))
    }


    fn concurrency(
        self,
        max: usize,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::limit::ConcurrencyLimitLayer, Middleware>, Serv>
    {
        self.chain(|sb| sb.concurrency_limit(max))
    }


    fn rate_limit(
        self,
        num: u64,
        per: std::time::Duration,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::limit::RateLimitLayer, Middleware>, Serv> {
        self.chain(|sb| sb.rate_limit(num, per))
    }


    fn retry<P>(
        self,
        policy: P,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::retry::RetryLayer<P>, Middleware>, Serv> {
        self.chain(|sb| sb.retry(policy))
    }

    fn timeout(
        self,
        timeout: std::time::Duration,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::timeout::TimeoutLayer, Middleware>, Serv> {
        self.chain(|sb| sb.timeout(timeout))
    }


    fn filter<P>(
        self,
        predicate: P,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::filter::FilterLayer<P>, Middleware>, Serv> {
        self.chain(|sb| sb.filter(predicate))
    }


    fn filter_async<P>(
        self,
        predicate: P,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::filter::AsyncFilterLayer<P>, Middleware>, Serv>
    {
        self.chain(|sb| sb.filter_async(predicate))
    }

    fn map_request<F, R1, R2>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::MapRequestLayer<F>, Middleware>, Serv>
    where
        F: FnMut(R1) -> R2 + Clone,
    {
        self.chain(|sb| sb.map_request(f))
    }

    fn map_response<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::MapResponseLayer<F>, Middleware>, Serv>
    {
        self.chain(|sb| sb.map_response(f))
    }

    fn map_err<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::MapErrLayer<F>, Middleware>, Serv> {
        self.chain(|sb| sb.map_err(f))
    }

    fn map_future<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::MapFutureLayer<F>, Middleware>, Serv> {
        self.chain(|sb| sb.map_future(f))
    }

    fn then<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::ThenLayer<F>, Middleware>, Serv> {
        self.chain(|sb| sb.then(f))
    }

    fn and_then<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::AndThenLayer<F>, Middleware>, Serv> {
        self.chain(|sb| sb.and_then(f))
    }

    fn map_result<F>(
        self,
        f: F,
    ) -> WorkerBuilder<Req, Ctx, (), Stack<tower::util::MapResultLayer<F>, Middleware>, Serv> {
        self.chain(|sb| sb.map_result(f))
    }

    /// Catch panics in execution and pipe them as errors
    fn catch_panic(
        self,
    ) -> WorkerBuilder<
        Req,
        Ctx,
        (),
        Stack<
            CatchPanicLayer<fn(Box<dyn std::any::Any + Send>) -> NovaRuntimeError>,
            Middleware,
        >,
        Serv,
    > {
        self.chain(|svc| svc.layer(CatchPanicLayer::new()))
    }


    // fn enable_tracing(
    //     self,
    // ) -> WorkerBuilder<Req, Ctx, (), Stack<tracing::TraceLayer, Middleware>, Serv> {
    //     use tracing::TraceLayer;
    //
    //     self.chain(|svc| svc.layer(TraceLayer::new()))
    // }
}
