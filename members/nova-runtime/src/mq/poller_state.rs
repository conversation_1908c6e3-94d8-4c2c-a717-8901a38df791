use std::sync::Arc;
use std::sync::atomic::AtomicUsize;
use std::sync::atomic::Ordering;

pub(crate) const STOPPED: usize = 2;
pub(crate) const PLUGGED: usize = 1;
pub(crate) const UNPLUGGED: usize = 0;

///任务池拉取状态
#[derive(Debug, Clone)]
pub struct PollerState {
    pub state: Arc<AtomicUsize>,
}

impl PollerState {

    /// 初始化
    pub fn new() -> Self {
        PollerState {
            state: Arc::new(AtomicUsize::new(PLUGGED)),
        }
    }

    /// Sets the state of the PollerState to `PLUGGED`.
    pub fn plug(&self) {
        self.state.store(PLUGGED, Ordering::Relaxed);
    }

    /// Sets the state of the PollerState to `UNPLUGGED`.
    pub fn unplug(&self) {
        self.state.store(UNPLUGGED, Ordering::Relaxed);
    }

    /// Returns `true` if the current state is `PLUGGED`.
    pub fn is_plugged(&self) -> bool {
        self.state.load(Ordering::Relaxed) == PLUGGED
    }

    /// Sets the state of the PollerState to `Stopped`.
    pub fn stop(&self) {
        self.state.store(STOPPED, Ordering::Relaxed);
    }

    /// Returns `true` if the current state is `STOPPED`.
    pub fn is_stopped(&self) -> bool {
        self.state.load(Ordering::Relaxed) == STOPPED
    }
}

impl Default for PollerState {
    fn default() -> Self {
        Self::new()
    }
}
