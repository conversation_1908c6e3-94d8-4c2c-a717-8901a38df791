use std::{
    pin::Pin,
    sync::atomic::Ordering,
    task::{Context as StdContext, Poll},
};
use crate::worker::Context;
use futures::{Stream, StreamExt};
use futures::stream::FusedStream;
use pin_project_lite::pin_project;
use crate::codec::Codec;
use crate::error::NovaRuntimeError;
use crate::worker::{Poller, PollerState, Worker, STOPPED};


///任务池
pub trait TaskSource<Req> {
    ///任务池任务流
    type Stream: Stream<Item = Result<Option<Req>, NovaRuntimeError>>;

    /// 任务流装饰器
    type Layer;


    /// 任务流编码解码器
    type Codec: Codec;


    /// 任务流轮询器
    /// 任务流轮询器负责从任务流中轮询任务，并将任务传递给任务处理函数
    fn poll(self, worker: &Worker<Context>) -> Poller<Self::Stream, Self::Layer>;
}

pin_project! {
    /// 任务流轮询器状态
    /// 任务流轮询器状态用于控制任务流的轮询状态，例如是否已停止、是否已连接等
    #[derive(Debug)]
    pub struct TaskSourceStream<S> {
        #[pin]
        stream: S,
        poller_state: PollerState,
    }
}

impl<S> TaskSourceStream<S> {
    /// 创建一个新的任务流轮询器状态
    pub fn new(stream: S, poller_state: PollerState) -> Self {
        Self { stream, poller_state }
    }
}

/// 任务流轮询器
/// 任务流轮询器负责从任务流中轮询任务，并将任务传递给任务处理函数
impl<S: Stream<Item = T> + Unpin, T> Stream for TaskSourceStream<S> {
    type Item = T;

    /// 任务流轮询器轮询任务
    fn poll_next(self: Pin<&mut Self>, cx: &mut StdContext<'_>) -> Poll<Option<Self::Item>> {
        let this = self.get_mut();
        if this.poller_state.is_plugged() {
            match this.stream.poll_next_unpin(cx) {
                Poll::Ready(Some(item)) => Poll::Ready(Some(item)),
                Poll::Ready(None) => Poll::Ready(None), // Inner stream is exhausted
                Poll::Pending => Poll::Pending,
            }
        } else if this.poller_state.is_stopped() {
            Poll::Ready(None)
        } else {
            Poll::Pending
        }
    }

    /// 任务流轮询器大小提示
    /// 任务流轮询器大小提示用于估计任务流中剩余的任务数量
    fn size_hint(&self) -> (usize, Option<usize>) {
        self.stream.size_hint()
    }
}

/// 任务流轮询器终止状态
/// FusedStream 保证结束后立即返回 None
impl<S: Unpin + Stream> FusedStream for TaskSourceStream<S> {
    fn is_terminated(&self) -> bool {
        self.poller_state.state.load(Ordering::Relaxed) == STOPPED
    }
}