pub mod config;
pub mod error;
mod server;
mod router;
mod domain;
pub mod queue;

use std::io::Error;
use serde::{Deserialize, Serialize};
use tracing::info;
pub use config::*;
pub use error::*;
pub use server::*;
pub use router::*;
pub use domain::*;
pub use queue::*;

pub mod prelude {
    pub use nova_core::util::*;
}

///关闭进程
pub async fn shutdown(shutdown_tx: tokio::sync::broadcast::Sender<()>) {
    tokio::select! {
        _ = tokio::signal::ctrl_c() => {
            info!(" initiating shutdown...");
        }
    }
    //向所有的任务发送关闭信号
    let _ = shutdown_tx.send(());
}


#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct Email {
    pub to: String,
    pub subject: String,
    pub text: String,
}
pub async fn send_email(email: Email) -> anyhow::Result<(), Error> {
    info!("Attempting to send email to {:?}", email.text);
    Ok(())
}