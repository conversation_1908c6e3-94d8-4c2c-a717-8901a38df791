
use serde::{Deserialize, Serialize};
use std::{
    fmt::{Display, Formatter},
    str::FromStr,
};
use crate::{InternalError, NovaError};

#[derive(Debug, Copy, Clone, Eq, PartialEq, Ord, PartialOrd, Deserialize, Serialize, Hash)]
#[serde(rename_all = "camelCase")]
pub enum Environment {
    Test,
    Development,
    Production,
}

impl Environment {
    pub fn is_production(&self) -> bool {
        matches!(self, Environment::Production)
    }
}

impl TryFrom<&str> for Environment {
    type Error = NovaError;

    fn try_from(value: &str) -> Result<Self, Self::Error> {
        match value {
            "test" => Ok(Environment::Test),
            "development" => Ok(Environment::Development),
            "production" => Ok(Environment::Production),
            _ => Err(InternalError::configuration_error(
                &format!("Invalid environment: {}", value),
                None,
            )),
        }
    }
}

impl FromStr for Environment {
    type Err = NovaError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Self::try_from(s)
    }
}

impl Display for Environment {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        let environment = match self {
            Environment::Test => "test",
            Environment::Development => "development",
            Environment::Production => "production",
        };
        write!(f, "{environment}")
    }
}
