use std::marker::PhantomData;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use crate::BoxDynError;

pub trait Codec {
    /// The mode of storage by the codec
    type Compact;
    /// Error encountered by the codec
    type Error: Into<BoxDynError>;
    /// The encoding method
    fn encode<I>(input: I) -> Result<Self::Compact, Self::Error>
    where
        I: Serialize;
    /// The decoding method
    fn decode<O>(input: Self::Compact) -> Result<O, Self::Error>
    where
        O: for<'de> Deserialize<'de>;
}


/// Json encoding and decoding
#[derive(Debug, <PERSON>lone, Default)]
pub struct JsonCodec<Output> {
    _o: PhantomData<Output>,
}

#[derive(Debug, Clone)]
pub struct NoopCodec<Compact> {
    compact: PhantomData<Compact>,
}

impl<Output> Codec for NoopCodec<Output> {
    type Compact = Output;
    type Error = BoxDynError;

    fn encode<I>(_: I) -> Result<Self::Compact, Self::Error>
    where
        I: Serialize,
    {
        unreachable!("NoopCodec doesn't have decoding functionality")
    }
    fn decode<O>(_: Self::Compact) -> Result<O, Self::Error>
    where
        O: for<'de> Deserialize<'de>,
    {
        unreachable!("NoopCodec doesn't have decoding functionality")
    }
}

impl Codec for JsonCodec<Vec<u8>> {
    type Compact = Vec<u8>;
    type Error = serde_json::Error;
    fn encode<T: Serialize>(input: T) -> Result<Vec<u8>, Self::Error> {
        serde_json::to_vec(&input)
    }

    fn decode<O>(compact: Vec<u8>) -> Result<O, Self::Error>
    where
        O: for<'de> Deserialize<'de>,
    {
        serde_json::from_slice(&compact)
    }
}

impl Codec for JsonCodec<String> {
    type Compact = String;
    type Error = serde_json::Error;
    fn encode<T: Serialize>(input: T) -> Result<String, Self::Error> {
        serde_json::to_string(&input)
    }

    fn decode<O>(compact: String) -> Result<O, Self::Error>
    where
        O: for<'de> Deserialize<'de>,
    {
        serde_json::from_str(&compact)
    }
}

impl Codec for JsonCodec<Value> {
    type Compact = Value;
    type Error = serde_json::Error;
    fn encode<T: Serialize>(input: T) -> Result<Value, Self::Error> {
        serde_json::to_value(input)
    }

    fn decode<O>(compact: Value) -> Result<O, Self::Error>
    where
        O: for<'de> Deserialize<'de>,
    {
        serde_json::from_value(compact)
    }
}