use std::fmt::Display;
use std::str::FromStr;
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use serde::de::Visitor;
use ulid::Ulid;
use crate::error::NovaRuntimeError;
use crate::service_fn::FromRequest;
use crate::task::TaskRequest;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>q, <PERSON>h, PartialEq)]
pub struct TaskId(Ulid);


impl TaskId {
    pub fn new() -> Self {
        Self(Ulid::new())
    }
    pub fn inner(&self) -> Ulid {
        self.0
    }
}

impl Default for TaskId {
    fn default() -> Self {
        Self::new()
    }
}

impl FromStr for TaskId {
    type Err = ulid::DecodeError;
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(TaskId(Ulid::from_str(s)?))
    }
}

impl Display for TaskId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        Display::fmt(&self.0, f)
    }
}


impl<Req, Ctx> FromRequest<TaskRequest<Req, Ctx>> for TaskId {
    fn from_request(req: &TaskRequest<Req, Ctx>) -> Result<Self, NovaRuntimeError> {
        Ok(req.parts.task_id.clone())
    }
}

impl Serialize for TaskId {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}

impl<'de> Deserialize<'de> for TaskId {
    fn deserialize<D>(deserializer: D) -> Result<TaskId, D::Error>
    where
        D: Deserializer<'de>,
    {
        deserializer.deserialize_str(TaskIdVisitor)
    }
}

struct TaskIdVisitor;

impl Visitor<'_> for TaskIdVisitor {
    type Value = TaskId;

    fn expecting(&self, formatter: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        formatter.write_str("a `ulid`")
    }

    fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
    where
        E: serde::de::Error,
    {
        TaskId::from_str(value).map_err(serde::de::Error::custom)
    }
}