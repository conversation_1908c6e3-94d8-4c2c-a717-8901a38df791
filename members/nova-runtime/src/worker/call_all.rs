//! # CallAll 模块
//!
//! 这个模块提供了用于并发处理流中所有请求的组合器。
//!
//! ## 主要组件
//!
//! - [`CallAllUnordered`]: 无序处理所有请求的高级组合器
//! - [`CallAll`]: 核心的请求处理逻辑，支持不同的队列策略
//! - [`Drive`]: 抽象的 Future 队列管理 trait
//!
//! ## 工作原理
//!
//! 1. **请求流处理**: 从输入流中读取请求
//! 2. **服务调用**: 使用提供的服务处理每个请求
//! 3. **并发管理**: 通过队列管理多个并发的 Future
//! 4. **响应返回**: 将完成的响应作为流返回
//!
//! ## 使用场景
//!
//! - 批量处理 HTTP 请求
//! - 并发执行数据库查询
//! - 任务队列的工作者实现
//! - 任何需要并发处理流数据的场景

// 导入必要的异步和流处理相关的类型
use futures::{ready, stream::FuturesUnordered, Stream};
use pin_project_lite::pin_project;
use std::{
    fmt,
    future::Future,
    pin::Pin,
    task::{Context, Poll},
};
use tower::Service;
use tracing::info;

pin_project! {
    /// 无序调用所有任务的组合器
    ///
    /// 这个结构体用于并发处理来自流的所有请求，不保证响应的顺序。
    /// 它使用 `FuturesUnordered` 来管理并发的 Future，允许任务以任意顺序完成。
    ///
    /// # 泛型参数
    /// - `Svc`: 实现了 `Service` trait 的服务类型
    /// - `S`: 实现了 `Stream` trait 的流类型，提供请求数据
    #[derive(Debug)]
    pub(super) struct CallAllUnordered<Svc, S>
    where
        Svc: Service<S::Item>,  // 服务必须能够处理流中的项目类型
        S: Stream,              // S 必须是一个流
    {
        #[pin]
        inner: CallAll<Svc, S, FuturesUnordered<Svc::Future>>,  // 内部使用 CallAll 和 FuturesUnordered
    }
}

impl<Svc, S> CallAllUnordered<Svc, S>
where
    Svc: Service<S::Item>,
    S: Stream,
{
    /// 创建新的 [`CallAllUnordered`] 组合器
    ///
    /// 这个方法创建一个新的无序调用组合器，它将：
    /// 1. 从提供的流中读取请求
    /// 2. 使用提供的服务处理这些请求
    /// 3. 以任意顺序返回响应（不保证顺序）
    ///
    /// # 参数
    /// - `service`: 用于处理请求的服务实例
    /// - `stream`: 提供请求数据的流
    ///
    /// # 返回值
    /// 返回一个新的 `CallAllUnordered` 实例
    ///
    /// [`Stream`]: https://docs.rs/futures/latest/futures/stream/trait.Stream.html
    pub(super) fn new(service: Svc, stream: S) -> CallAllUnordered<Svc, S> {
        CallAllUnordered {
            // 使用 FuturesUnordered 作为内部队列，允许无序完成
            inner: CallAll::new(service, stream, FuturesUnordered::new()),
        }
    }
}

// 为 CallAllUnordered 实现 Stream trait，使其可以作为流使用
impl<Svc, S> Stream for CallAllUnordered<Svc, S>
where
    Svc: Service<S::Item>,
    S: Stream,
{
    /// 流产生的项目类型：服务的响应结果（成功或错误）
    type Item = Result<Svc::Response, Svc::Error>;

    /// 轮询下一个项目
    ///
    /// 这个方法委托给内部的 `CallAll` 实例来处理实际的轮询逻辑。
    /// 它会返回下一个完成的服务响应，不保证顺序。
    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        // 将轮询委托给内部的 CallAll 实例
        self.project().inner.poll_next(cx)
    }
}

// 为 FuturesUnordered 实现 Drive trait
// 这允许 FuturesUnordered 作为 CallAll 的队列驱动器
impl<F: Future> Drive<F> for FuturesUnordered<F> {
    /// 检查队列是否为空
    ///
    /// 当没有正在执行的 Future 时返回 true
    fn is_empty(&self) -> bool {
        FuturesUnordered::is_empty(self)
    }

    /// 将新的 Future 推入队列
    ///
    /// 这个 Future 将与其他 Future 并发执行，完成顺序不确定
    fn push(&mut self, future: F) {
        FuturesUnordered::push(self, future)
    }

    /// 轮询队列中的 Future，返回下一个完成的结果
    ///
    /// 这个方法会轮询所有正在执行的 Future，返回第一个完成的结果
    fn poll(&mut self, cx: &mut Context<'_>) -> Poll<Option<F::Output>> {
        Stream::poll_next(Pin::new(self), cx)
    }
}

pin_project! {
    /// 调用所有请求的核心结构体
    ///
    /// 这是一个通用的组合器，用于处理来自流的所有请求。
    /// 它管理服务的状态、请求流和响应队列。
    ///
    /// # 泛型参数
    /// - `Svc`: 处理请求的服务类型
    /// - `S`: 提供请求的流类型
    /// - `Q`: 管理并发 Future 的队列类型（实现 Drive trait）
    ///
    /// # 字段说明
    /// - `service`: 可选的服务实例，用于处理请求
    /// - `stream`: 提供请求数据的流（使用 pin 投影）
    /// - `queue`: 管理正在执行的 Future 的队列
    /// - `eof`: 标记流是否已结束（End of File）
    /// - `curr_req`: 当前正在处理的请求（如果有的话）
    pub(crate) struct CallAll<Svc, S, Q>
    where
        S: Stream,
    {
        service: Option<Svc>,           // 服务实例，Option 允许在错误后取出
        #[pin]
        stream: S,                      // 请求流，需要 pin 投影
        queue: Q,                       // Future 队列
        eof: bool,                      // 流结束标志
        curr_req: Option<S::Item>       // 当前请求缓存
    }
}

// 为 CallAll 实现 Debug trait，用于调试输出
impl<Svc, S, Q> fmt::Debug for CallAll<Svc, S, Q>
where
    Svc: fmt::Debug,
    S: Stream + fmt::Debug,
{
    /// 格式化 CallAll 结构体用于调试输出
    ///
    /// 输出包含服务、流和结束标志的信息，但不包含队列和当前请求
    /// （因为它们可能不实现 Debug 或包含敏感信息）
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("CallAll")
            .field("service", &self.service)    // 服务实例
            .field("stream", &self.stream)      // 请求流
            .field("eof", &self.eof)            // 结束标志
            .finish()
    }
}

/// Drive trait 定义了管理并发 Future 队列的接口
///
/// 这个 trait 抽象了不同类型的 Future 队列（如有序、无序等），
/// 允许 CallAll 与不同的队列实现配合工作。
///
/// # 泛型参数
/// - `F`: 队列中存储的 Future 类型
pub(crate) trait Drive<F: Future> {
    /// 检查队列是否为空
    ///
    /// # 返回值
    /// 当队列中没有正在执行的 Future 时返回 `true`
    fn is_empty(&self) -> bool;

    /// 将新的 Future 推入队列
    ///
    /// # 参数
    /// - `future`: 要添加到队列中的 Future
    fn push(&mut self, future: F);

    /// 轮询队列中的 Future
    ///
    /// 这个方法会检查队列中的 Future，返回下一个完成的结果。
    /// 具体的轮询策略（有序/无序）由实现决定。
    ///
    /// # 参数
    /// - `cx`: 异步上下文，包含 Waker 用于唤醒任务
    ///
    /// # 返回值
    /// - `Poll::Ready(Some(output))`: 有 Future 完成，返回其输出
    /// - `Poll::Ready(None)`: 队列为空且没有更多 Future
    /// - `Poll::Pending`: 没有 Future 完成，需要等待
    fn poll(&mut self, cx: &mut Context<'_>) -> Poll<Option<F::Output>>;
}

impl<Svc, S, Q> CallAll<Svc, S, Q>
where
    Svc: Service<S::Item>,  // 服务必须能处理流中的项目
    S: Stream,              // S 必须是流
    Q: Drive<Svc::Future>,  // Q 必须能驱动服务返回的 Future
{
    /// 创建新的 CallAll 实例
    ///
    /// 这是一个 const 函数，可以在编译时创建实例。
    ///
    /// # 参数
    /// - `service`: 用于处理请求的服务实例
    /// - `stream`: 提供请求的流
    /// - `queue`: 管理并发 Future 的队列
    ///
    /// # 返回值
    /// 返回初始化的 CallAll 实例
    pub(crate) const fn new(service: Svc, stream: S, queue: Q) -> CallAll<Svc, S, Q> {
        CallAll {
            service: Some(service),     // 将服务包装在 Option 中
            stream,                     // 请求流
            queue,                      // Future 队列
            eof: false,                 // 初始时流未结束
            curr_req: None,             // 初始时没有当前请求
        }
    }
}

// 为 CallAll 实现 Stream trait，使其可以产生服务响应
impl<Svc, S, Q> Stream for CallAll<Svc, S, Q>
where
    Svc: Service<S::Item>,  // 服务必须能处理流中的项目
    S: Stream,              // S 必须是流
    Q: Drive<Svc::Future>,  // Q 必须能驱动服务的 Future
{
    /// 流产生的项目类型：服务响应的结果
    type Item = Result<Svc::Response, Svc::Error>;

    /// 轮询下一个响应
    ///
    /// 这是 CallAll 的核心逻辑，它协调以下几个组件：
    /// 1. 检查是否有已完成的响应可以返回
    /// 2. 检查流是否已结束以及是否所有任务都已完成
    /// 3. 确保服务准备好处理新请求
    /// 4. 从流中获取新请求并提交给服务
    ///
    /// # 参数
    /// - `cx`: 异步上下文，用于唤醒任务
    ///
    /// # 返回值
    /// - `Poll::Ready(Some(Ok(response)))`: 有响应完成
    /// - `Poll::Ready(Some(Err(error)))`: 服务出错
    /// - `Poll::Ready(None)`: 所有请求都已处理完成
    /// - `Poll::Pending`: 需要等待更多数据或响应
    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        // 获取结构体字段的可变引用（pin 投影）
        let mut this = self.project();

        loop {
            // 第一步：检查是否有已完成的响应可以返回
            // 优先返回已完成的响应，这样可以尽快释放资源
            if let Poll::Ready(r) = this.queue.poll(cx) {
                if let Some(rsp) = r.transpose()? {
                    // 有响应完成，立即返回
                    return Poll::Ready(Some(Ok(rsp)));
                }
                // 如果 r 是 None，说明队列为空，继续下面的逻辑
            }

            // 第二步：如果没有更多请求，检查是否完全完成
            if *this.eof {
                if this.queue.is_empty() {
                    // 流已结束且队列为空，所有工作都已完成
                    return Poll::Ready(None);
                } else {
                    // 流已结束但还有正在执行的任务，等待它们完成
                    return Poll::Pending;
                }
            }

            // 第三步：检查服务是否准备好处理新请求
            let svc = this
                .service
                .as_mut()
                .expect("在提取内部服务后使用 CallAll");

            if let Err(e) = ready!(svc.poll_ready(cx)) {
                // 服务出错，设置 eof 标志防止再次调用服务
                // 这确保了在 poll_ready 错误后不会再尝试使用服务
                *this.eof = true;
                return Poll::Ready(Some(Err(e)));
            }

            // 第四步：如果没有存储的请求，从流中获取下一个请求
            // 如果流还没准备好，返回 Pending
            if this.curr_req.is_none() {
                *this.curr_req = match ready!(this.stream.as_mut().poll_next(cx)) {
                    Some(next_req) => Some(next_req),
                    None => {
                        // 流已结束，标记 eof 并继续循环
                        // 这将在下次循环时触发完成检查
                        *this.eof = true;
                        continue;
                    }
                };
            }

            // 第五步：处理当前请求
            // 注意：上面的检查确保了 curr_req 一定有值，所以 unwrap 是安全的
            let request = this.curr_req.take().unwrap();
            info!("开始调用服务处理请求: {:?}", request);
            // 调用服务处理请求，并将返回的 Future 推入队列
            // 这个 Future 将与其他 Future 并发执行
            this.queue.push(svc.call(request));

            // 继续循环，可能会立即返回已完成的响应
        }
    }
}
