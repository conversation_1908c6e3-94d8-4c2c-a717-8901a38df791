use fred::prelude::Error;
use nova_runtime::codec::Codec;
use nova_runtime::codec::JsonCodec;
use nova_runtime::mq::{Stat, WorkerStat};
use nova_runtime::mq::TaskSourceExpose;
use nova_runtime::task::TaskRequest;
use serde::de::DeserializeOwned;
use serde::Serialize;

use crate::redis_task_source::RedisContext;
use crate::redis_task_source::RedisStorage;

type RedisCodec = JsonCodec<Vec<u8>>;

/// 任务源暴露实现
impl<T> TaskSourceExpose<T> for RedisStorage<T>
where
    T: 'static + Serialize + DeserializeOwned + Send + Unpin + Sync,
{
    type Request = TaskRequest<T, RedisContext>;
    type Error = Error;

    async fn stats(&self) -> Result<Vec<Stat>, Self::Error> {
        todo!()
    }

    async fn get_worker_state(&self) -> Result<Vec<WorkerStat>, Self::Error> {
        todo!()
    }
}
