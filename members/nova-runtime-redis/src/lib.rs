mod expose;
mod redis_task_source;

use anyhow::Result;
use fred::prelude::Client;
use fred::prelude::*;
use fred::util as fred_utils;
pub use redis::aio::ConnectionManager;
pub use redis_task_source::Config;
pub use redis_task_source::RedisContext;
pub use redis_task_source::RedisPollError;
pub use redis_task_source::RedisQueueInfo;
pub use redis_task_source::RedisScript;
pub use redis_task_source::RedisStorage;

/// 创建redis脚本
///
/// 并行加载所有 Lua 脚本以提高性能，自动检查脚本是否已存在
pub async fn create_redis_script(client: &Client) -> Result<RedisScript> {
    /// 辅助函数：加载单个脚本
    async fn load_script(client: &Client, content: &str) -> Result<String> {
        let hash = fred_utils::sha1_hash(content);
        // 检查脚本是否已存在，如果不存在则加载
        if !client.script_exists::<bool, _>(&hash).await? {
            let _: () = client.script_load(content).await?;
        }
        Ok(hash)
    }

    // 并行加载所有脚本
    let (
        done_job,
        push_job,
        retry_job,
        enqueue_scheduled,
        get_jobs,
        register_consumer,
        kill_job,
        re_enqueue_active,
        re_enqueue_orphaned,
        schedule_job,
        vacuum,
        stats,
    ) = futures::try_join!(
        load_script(client, include_str!("../lua/done_job.lua")),
        load_script(client, include_str!("../lua/push_job.lua")),
        load_script(client, include_str!("../lua/retry_job.lua")),
        load_script(client, include_str!("../lua/enqueue_scheduled_jobs.lua")),
        load_script(client, include_str!("../lua/get_jobs.lua")),
        load_script(client, include_str!("../lua/register_consumer.lua")),
        load_script(client, include_str!("../lua/kill_job.lua")),
        load_script(client, include_str!("../lua/re_enqueue_active_jobs.lua")),
        load_script(client, include_str!("../lua/re_enqueue_orphaned_jobs.lua")),
        load_script(client, include_str!("../lua/schedule_job.lua")),
        load_script(client, include_str!("../lua/vacuum.lua")),
        load_script(client, include_str!("../lua/stats.lua")),
    )?;

    Ok(RedisScript {
        done_job,
        push_job,
        retry_job,
        enqueue_scheduled,
        get_jobs,
        register_consumer,
        kill_job,
        re_enqueue_active,
        re_enqueue_orphaned,
        schedule_job,
        vacuum,
        stats,
    })
}
