--[[
推送新任务到队列

这个脚本用于将新任务添加到任务队列中。它确保任务数据的原子性存储，
并在成功添加任务后通知工作者有新任务可处理。

参数说明：
KEYS[1]: 任务数据哈希表 (hash) - 存储任务ID到任务数据的映射 data
KEYS[2]: 活跃任务列表 (list) - 存储等待处理的任务ID队列 active
KEYS[3]: 近一小时下发统计  - 用于通知工作者有新任务 received
KEYS[4]: 任务关联执行器 - 用于查询任务关联的执行器 jobs

ARGV[1]: 任务ID - 唯一标识任务的字符串
ARGV[2]: 序列化的任务数据 - 包含任务执行所需的所有信息
ARGV[3]: 执行器ID - 唯一标识执行器的字符串

返回值：
1 - 任务成功入队（新任务）
0 - 任务已存在，未重复添加
--]]

-- 使用 hsetnx 原子性地设置任务数据
-- hsetnx 只在字段不存在时才设置，避免重复任务
local set = redis.call("hsetnx", KEYS[1], ARGV[1], ARGV[2])

-- 如果任务数据成功设置（新任务）
if set == 1 then
  -- 将任务ID推入活跃任务列表的尾部
  -- 使用 rpush 确保任务按照提交顺序被处理（FIFO）
  redis.call("rpush", KEYS[2], ARGV[1])

  redis.call("set", KEYS[4], ARGV[3])

  -- 近一小时下发数量统计
  redis.call("zincrby", KEYS[3], 1, ARGV[1])
  -- 过期时间设置为7天
  redis.call("expire", KEYS[3], 60 * 60 * 24 * 7)
end

-- 返回操作结果：1表示新任务入队，0表示任务已存在
return set
