use serde::Deserialize;
use serde::Serialize;

pub mod poller;
pub mod poller_state;
pub mod task_source;

/// 任务源暴露 trait
pub trait TaskSourceExpose<T>
where
    Self: Sized,
{
    /// 任务请求类型
    type Request;

    /// 任务源错误类型
    type Error;

    /// 统计任务源中的执行器阻塞数量
    /// 以便于启动对应的worker
    async fn stats(&self) -> Result<Vec<Stat>, Self::Error>;

    /// 获取任务源中的执行器状态
    ///获取正在运行的任务数量
    async fn get_worker_state(&self) -> Result<Vec<WorkerStat>, Self::Error>;
}

/// 任务源统计信息
#[derive(Debug, Deserialize, Serialize, Default)]
pub struct Stat {
    /// 执行器名称
    pub worker_name: String,
    /// 待消费的任务数量
    pub pending: usize,
}

/// 任务源统计信息
#[derive(Debug, Deserialize, Serialize, Default)]
pub struct WorkerStat {
    /// 执行器名称
    pub worker_name: String,
    /// 待消费的任务数量
    pub pending: usize,
    /// 正在运行的任务数量
    pub running: usize,
    /// 已失败的任务数量
    pub failed: usize,
    /// 已成功的任务数量
    pub success: usize,
    /// 平均响应时间
    pub rtt: f64,
    /// 最近一次运行时间
    pub last_running_time: u64,
}
